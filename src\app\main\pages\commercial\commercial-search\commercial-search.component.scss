.commercial-search {
  display: flex;
  height: 100%;
  background: #f8fafc;
  font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI',
    sans-serif;
  color: #0f172a;
}

/* LEFT FILTER */

.search-filter {
  width: 260px;
  background: #ffffff;
  border-right: 1px solid #e5e7eb;
  padding: 24px 20px;
  overflow-y: auto;
}

.filter-header {
  .filter-title {
    font-size: 15px;
    font-weight: 700;
    color: #111827;
  }

  .filter-collapse-icon {
    width: 18px;
    height: 18px;
    color: #9ca3af;
  }
}

.filter-item {
  margin-bottom: 8px;

  .filter-checkbox {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 13px;
    color: #4b5563;

    input {
      width: 16px;
      height: 16px;
      margin-right: 8px;
      border-radius: 4px;
      border: 1px solid #d1d5db;
      cursor: pointer;
    }

    .filter-label {
      .filter-count {
        color: #9ca3af;
        margin-left: 4px;
      }
    }

    &:hover .filter-label {
      color: #1d4ed8;
    }
  }
}

/* MAIN */

.search-main {
  flex: 1;
  padding: 24px 32px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.search-main-header {
  .search-title {
    font-size: 22px;
    font-weight: 700;
  }

  .search-subtitle {
    font-size: 13px;
    color: #6b7280;
  }

  .search-upload-btn {
    font-size: 13px;
    padding: 8px 16px;
    border-radius: 10px;
  }
}

/* SEARCH INPUT */

.search-input-wrapper {
  max-width: auto;
  // max-width: 720px;
}

.search-input-inner {
  width: 100%;
  background: #ffffff;
  border-radius: 12px;
  border: 2px solid #e5e7eb;
  padding: 8px 12px;
  transition: all 0.15s ease;
  box-shadow: 0 1px 2px rgba(15, 23, 42, 0.04);

  &.search-input-focused {
    border-color: #3b82f6;
    background: #eff6ff;
    box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.12);
  }
}

.search-icon {
  width: 18px;
  height: 18px;
  color: #9ca3af;
  margin-right: 8px;
}

.search-input {
  flex: 1 1 auto;
  min-width: 0;
  border: none;
  box-shadow: none;
  padding-left: 0;
  background: transparent;
  font-size: 13px;
  color: #111827;

  &:focus {
    outline: none;
    box-shadow: none;
  }

  &::placeholder {
    color: #9ca3af;
  }
}
.search-btn {
  white-space: nowrap;
  padding-inline: 1.25rem;
}
.btn-clear {
  border: none;
  background: transparent; 
  padding: 4px;
  border-radius: 6px;
  color: #9ca3af;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    background: #e5e7eb;
    color: #4b5563;
  }

  i {
    width: 14px;
    height: 14px;
  }
}

/* TABS */

.search-tabs {
  border-bottom: 1px solid #e5e7eb;
  margin-top: 8px;
}

.search-tab {
  border: none;
  background: transparent;
  padding: 10px 0;
  margin-right: 24px;
  font-size: 13px;
  font-weight: 600;
  color: #6b7280;
  border-bottom: 2px solid transparent;
  transition: all 0.15s ease;

  &.search-tab-active {
    color: #2563eb;
    border-color: #2563eb;
  }

  &:hover:not(.search-tab-active) {
    color: #111827;
  }
}

/* SEARCH RESULT INFO */

.search-result-info {
  margin-top: 12px;
  padding: 10px 14px;
  border-radius: 10px;
  background: #eff6ff;
  border: 1px solid #bfdbfe;
  font-size: 12px;
  color: #1d4ed8;
  display: flex;
  justify-content: space-between;
  align-items: center;

  strong {
    font-weight: 600;
  }
}

.btn-link-clear {
  border: none;
  background: none;
  padding: 0;
  font-size: 12px;
  font-weight: 500;
  color: #1d4ed8;
  text-decoration: underline;
}

/* EMPTY STATE */

.search-empty {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
  text-align: center;

  h4 {
    font-size: 16px;
    font-weight: 600;
    color: #111827;
  }

  p {
    font-size: 13px;
    color: #6b7280;
  }
}

.search-empty-icon {
  width: 72px;
  height: 72px;
  border-radius: 999px;
  background: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 14px;

  i {
    width: 32px;
    height: 32px;
    color: #d1d5db;
  }
}

/* TABLE */

.search-table-wrapper {
  background: #ffffff;
  border-radius: 18px;
  border: 1px solid #e5e7eb;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(15, 23, 42, 0.04);
  display: flex;
  flex-direction: column;
}

.search-table-header {
  padding: 12px 20px;
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
  font-size: 11px;
  font-weight: 600;
  color: #6b7280;
  text-transform: uppercase;

  .col-name {
    padding-right: 12px;
  }

  .col-type,
  .col-size,
  .col-date,
  .col-action {
    width: 120px;
    text-align: left;
  }

  .col-action {
    text-align: center;
  }
}

.search-table-body {
  max-height: calc(100vh - 260px);
  overflow-y: auto;
}

.search-row {
  padding: 10px 20px;
  font-size: 13px;
  border-bottom: 1px solid #f3f4f6;
  background: #ffffff;
  transition: background 0.12s ease;

  &:hover {
    background: #f9fafb;
  }

  .file-icon {
    width: 32px;
    height: 32px;
    border-radius: 10px;
    background: #eef2ff;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 10px;

    i {
      width: 16px;
      height: 16px;
      color: #4f46e5;
    }
  }

  .file-name {
    max-width: 420px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-weight: 500;
    color: #111827;
  }

  .col-type,
  .col-size,
  .col-date,
  .col-action {
    width: 120px;
  }

  .col-size,
  .col-date {
    color: #6b7280;
    font-size: 12px;
  }
}

.tag-pill {
  display: inline-block;
  padding: 2px 10px;
  border-radius: 999px;
  background: #f3f4f6;
  border: 1px solid #e5e7eb;
  font-size: 11px;
  color: #6b7280;
}

/* ACTION BUTTON */

.btn-icon {
  border: none;
  background: transparent;
  padding: 4px;
  border-radius: 8px;
  color: #9ca3af;

  i {
    width: 16px;
    height: 16px;
  }

  &:hover {
    background: #e5e7eb;
    color: #4b5563;
  }
}

/* PAGINATION */

.search-pagination {
  padding: 10px 18px;
  border-top: 1px solid #e5e7eb;
  background: #f9fafb;
  font-size: 12px;
  color: #6b7280;
}

.pagination-buttons {
  display: flex;
  align-items: center;
  gap: 4px;
}

.btn-page {
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  padding: 4px 10px;
  background: #ffffff;
  font-size: 12px;
  color: #4b5563;
  min-width: 32px;

  &:hover:not(:disabled) {
    background: #f3f4ff;
  }

  &:disabled {
    opacity: 0.4;
    cursor: default;
  }

  &.btn-page-active {
    background: #2563eb;
    border-color: #2563eb;
    color: #ffffff;
  }

  i {
    width: 14px;
    height: 14px;
  }
}

.btn-page-icon {
  padding: 4px 6px;
  min-width: 28px; 

  i {
    width: 10px;
    height: 10px;
  }
}

/* MOBILE */

@media (max-width: 991.98px) {
  .search-main {
    padding: 16px;
  }

  .search-table-header .col-type,
  .search-table-header .col-size,
  .search-table-header .col-date {
    display: none;
  }

  .search-row .col-type,
  .search-row .col-size,
  .search-row .col-date {
    display: none;
  }

  .search-row .file-name {
    max-width: 100%;
  }
}
