import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { shareReplay, tap, finalize } from 'rxjs/operators';

interface CacheEntry {
  data: any;
  timestamp: number;
}

@Injectable({
  providedIn: 'root'
})
export class GraphDataCacheService {
  private searchCache = new Map<string, CacheEntry>();
  private graphCache = new Map<string, CacheEntry>();
  private inflightSearchRequests = new Map<string, Observable<any>>();
  private inflightGraphRequests = new Map<string, Observable<any>>();
  
  private readonly TTL = 5 * 60 * 1000; // 5 minutes
  private readonly MAX_CACHE_SIZE = 10;

  generateFilterKey(filters: any): string {
    const sorted = Object.keys(filters)
      .sort()
      .reduce((acc, k) => ({ ...acc, [k]: filters[k] }), {});
    return JSON.stringify(sorted);
  }

  generateNodeIdsKey(nodeIds: string[]): string {
    return [...nodeIds].sort().join(',');
  }

  getCachedSearchResponse(key: string): any | null {
    const cached = this.searchCache.get(key);
    if (cached && Date.now() - cached.timestamp < this.TTL) {
      return cached.data;
    }
    if (cached) {
      this.searchCache.delete(key);
    }
    return null;
  }

  getCachedGraphResponse(key: string): any | null {
    const cached = this.graphCache.get(key);
    if (cached && Date.now() - cached.timestamp < this.TTL) {
      return cached.data;
    }
    if (cached) {
      this.graphCache.delete(key);
    }
    return null;
  }

  getOrCreateSearchRequest(key: string, requestFn: () => Observable<any>): Observable<any> {
    const cached = this.getCachedSearchResponse(key);
    if (cached) {
      return of(cached);
    }

    if (this.inflightSearchRequests.has(key)) {
      return this.inflightSearchRequests.get(key)!;
    }

    const request$ = requestFn().pipe(
      tap(data => {
        this.evictIfNeeded(this.searchCache);
        this.searchCache.set(key, { data, timestamp: Date.now() });
      }),
      shareReplay(1),
      finalize(() => this.inflightSearchRequests.delete(key))
    );

    this.inflightSearchRequests.set(key, request$);
    return request$;
  }

  getOrCreateGraphRequest(key: string, requestFn: () => Observable<any>): Observable<any> {
    const cached = this.getCachedGraphResponse(key);
    if (cached) {
      return of(cached);
    }

    if (this.inflightGraphRequests.has(key)) {
      return this.inflightGraphRequests.get(key)!;
    }

    const request$ = requestFn().pipe(
      tap(data => {
        this.evictIfNeeded(this.graphCache);
        this.graphCache.set(key, { data, timestamp: Date.now() });
      }),
      shareReplay(1),
      finalize(() => this.inflightGraphRequests.delete(key))
    );

    this.inflightGraphRequests.set(key, request$);
    return request$;
  }

  invalidateSearchCache(): void {
    this.searchCache.clear();
  }

  invalidateGraphCache(): void {
    this.graphCache.clear();
  }

  private evictIfNeeded(cache: Map<string, CacheEntry>): void {
    if (cache.size >= this.MAX_CACHE_SIZE) {
      const firstKey = cache.keys().next().value;
      cache.delete(firstKey);
    }
  }
}

