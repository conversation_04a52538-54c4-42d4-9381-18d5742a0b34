@import "@core/scss/angular/libs/datatables.component.scss";
@import "@core/scss/angular/libs/select.component.scss";
@import "@core/scss/angular/libs/date-time-picker.component.scss";
@import "@core/scss/angular/libs/flatpickr.component.scss";

.folder {
  overflow: auto;
}

.datatable-body-cell-label {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

.folder-item {
  border-radius: 0.375rem;
  cursor: pointer;
}

.folder-item:hover {
  // background: rgba(245, 247, 251, 1);
}

.wrap-text {
  word-wrap: break-word;
  /* Ng<PERSON>t từ khi cần */
  white-space: pre-wrap;
}

.spacing {
  display: inline-block;
  padding: 5px;
  border: 1px solid rgba(226, 232, 240, 1);

  &.active {
    background-color: $primary;
    color: #fff;
  }
}

.spacing:hover {
  background-color: $primary;
  color: #fff;
  cursor: pointer;
}

.save-file {
  position: fixed;
  bottom: 20px;
  right: 37%;
  cursor: pointer;
  z-index: 9999;
}

.selection-bar {
  background: white;
  border-radius: 12px;
  box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;
}

.close-btn {
  background: none;
  border: none;
  font-size: 20px;
}

.selected-text {
  font-weight: 500;
  font-size: 16px;
}

.save-btn {
  background: none;
  border: none;
  color: #2878f0;
  font-weight: 500;
  font-size: 16px;
}

.action-files {
  padding: 0 5px 0 0;
  margin: 0 5px 0 0;
  border-right: 1px solid rgba(117, 117, 117, 1);
  margin-left: 0;
}

#limit .ng-select-container {
  // height: 30px !important;
}

.p-autocomplete-items {
  padding: 0;
  margin: 0;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  max-height: 200px;
  overflow-y: auto;
  z-index: 1000;

  .p-autocomplete-item {
    padding: 8px 12px;
    cursor: pointer;
    transition: background-color 0.2s;
    white-space: normal;
    word-wrap: break-word;
    overflow-wrap: break-word;

    &:hover {
      background-color: #f5f5f5;
    }

    &.p-highlight {
      background-color: #e3f2fd;
      color: #1976d2;
    }
  }
}

.block-ui__element {
  height: 100%;
}

.content-body {
  height: 86vh;
  display: flex;
  flex-direction: column;
}

.cdk-virtual-scroll-content-wrapper {
  position: relative !important;
}

#detail-term .accordion .card .card-header button {
  padding: 0.5rem;
}

#fast-search .accordion .card .card-header button {
  padding: 0;
}

.fast-search {
  height: 85vh;
  overflow-y: auto;
  border-left: 1px solid #e0e0e0;
  transform: translateX(100%);
  /* mặc định ẩn */
  transition: transform 0.3s ease-in-out;
}

.fast-search.active {
  transform: translateX(0);
  /* hiện ra */
}

.fast-search.hidden {
  display: none;
}

.date-sm {
  height: 28px;
}

.copy-align-right {
  display: block;
  text-align: right;
}

#tim-kiem .ng2-flatpickr-input.form-control {
  padding: 0.188rem 0.857rem;
  font-size: 0.857rem;
  height: 2.142rem;
}

#tim-kiem .ng-select.ng-select-single .ng-select-container {
  height: 2.142rem;
}

.ng-select-multiple .ng-value-label {
  color: white;
}

.ng-select-multiple .ng-value-icon {
  color: white;
}

// Graph Modal Fullscreen Styles
::ng-deep .graph-modal-fullscreen {
  .modal-dialog {
    max-width: 100vw;
    width: 100vw;
    height: 100vh;
    margin: 0;
  }

  .modal-content {
    height: 100vh;
    border-radius: 0;
    border: none;
  }
}

.graph-modal-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
}

.graph-modal-header {
  border-bottom: 1px solid #e0e0e0;
  background-color: #fff;
  flex-shrink: 0;
}

.graph-modal-body {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

// Disabled state for graph button
.show-button-toggle.disabled {
  opacity: 0.5;
  pointer-events: none;
  cursor: not-allowed !important;
}

// Rounded modal styling for "Đồ thị" modal
::ng-deep .graph-modal-rounded {
  .modal-dialog {
    overflow: hidden;
  }

  .modal-content {
    border-radius: 0.5rem;
    border: 1px solid #dee2e6;
    overflow: hidden; // ensure inner content respects rounded corners
  }

  .graph-modal-container {
    height: 100%;
    width: 100%;
    border-radius: inherit;
    overflow: hidden;
    background: #fff;
  }
}