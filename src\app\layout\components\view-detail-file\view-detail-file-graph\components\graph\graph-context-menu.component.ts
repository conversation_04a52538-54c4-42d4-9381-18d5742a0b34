import {
  Component,
  Input,
  Output,
  EventEmitter,
  OnChanges,
  SimpleChanges,
} from '@angular/core';
import { ContextMenuItem } from '../../types/graph.types';

@Component({
  selector: 'app-graph-context-menu',
  templateUrl: './graph-context-menu.component.html',
  styleUrls: ['./graph-context-menu.component.scss'],
})
export class GraphContextMenuComponent implements OnChanges {
  @Input() visible: boolean = false;
  @Input() position: { x: number; y: number } = { x: 0, y: 0 };
  @Input() menuItem: ContextMenuItem | null = null;
  @Input() showExpandSubmenu: boolean = false;

  @Output() expandWithDefault = new EventEmitter<void>();
  @Output() expandWithConditions = new EventEmitter<void>();
  @Output() restoreNode = new EventEmitter<void>();
  @Output() collapseNode = new EventEmitter<void>();
  @Output() toggleExpandSubmenu = new EventEmitter<Event>();
  @Output() showSubmenu = new EventEmitter<void>();
  @Output() hideSubmenu = new EventEmitter<void>();
  @Output() close = new EventEmitter<void>();

  private submenuPinned: boolean = false;
  private submenuHideTimeout?: ReturnType<typeof setTimeout>;

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['visible'] && !changes['visible'].currentValue) {
      this.resetSubmenuState();
    }
  }

  onExpandWithDefault(event: Event): void {
    event.stopPropagation();
    this.expandWithDefault.emit();
    this.resetSubmenuState();
    this.close.emit();
  }

  onExpandWithConditions(event: Event): void {
    event.stopPropagation();
    this.expandWithConditions.emit();
    this.resetSubmenuState();
    this.close.emit();
  }

  onRestoreNode(event: Event): void {
    event.stopPropagation();
    this.restoreNode.emit();
    this.resetSubmenuState();
    this.close.emit();
  }

  onCollapseNode(event: Event): void {
    event.stopPropagation();
    this.collapseNode.emit();
    this.resetSubmenuState();
    this.close.emit();
  }

  onMenuClick(event: Event): void {
    event.stopPropagation();
  }

  onSubmenuMouseEnter(): void {
    this.clearSubmenuHideTimeout();
    // Emit event to parent instead of modifying @Input directly
    this.showSubmenu.emit();
  }

  onSubmenuMouseLeave(): void {
    if (this.submenuPinned) {
      return;
    }
    this.clearSubmenuHideTimeout();
    this.submenuHideTimeout = setTimeout(() => {
      // Emit event to parent instead of modifying @Input directly
      this.hideSubmenu.emit();
      this.submenuHideTimeout = undefined;
    }, 200);
  }

  onSubmenuToggleClick(event: Event): void {
    event.stopPropagation();
    this.submenuPinned = true;
    this.clearSubmenuHideTimeout();
    // Emit event to parent - parent will toggle the state
    // Don't modify @Input() directly - let parent handle it
    this.toggleExpandSubmenu.emit(event);
  }

  private clearSubmenuHideTimeout(): void {
    if (this.submenuHideTimeout) {
      clearTimeout(this.submenuHideTimeout);
      this.submenuHideTimeout = undefined;
    }
  }

  private resetSubmenuState(): void {
    this.clearSubmenuHideTimeout();
    this.submenuPinned = false;
    // Don't modify @Input() directly - parent will handle it via ngOnChanges
  }
}

