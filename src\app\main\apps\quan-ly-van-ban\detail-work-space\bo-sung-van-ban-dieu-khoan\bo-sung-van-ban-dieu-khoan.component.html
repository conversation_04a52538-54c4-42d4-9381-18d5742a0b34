<core-card [isReload]="isReload" class="card overflow-hidden">
  <div class="content-body">
    <section id="ngx-datatable-kitchen-sink">
      <div class="d-flex">
        <div class="card-body px-1 py-0">
          <form class="form form-vertical">
            <div class="bo-sung-van-ban-dieu-khoan-search-container row mt-1">
              <img
                *ngIf="!isInModal"
                src="assets/images/icons/x.svg"
                alt="x"
                class="bo-sung-van-ban-dieu-khoan-close-icon"
                (click)="closeBoSung()"
              />
              <div class="col-12 col-sm-10 col-xxl-8">
                <label class="label font-weight-bolder">Tìm kiếm </label>
                <input
                  type="text"
                  [(ngModel)]="searchString"
                  class="form-control"
                  [ngModelOptions]="{ standalone: true }"
                  placeholder="Nhập nội dung cần tìm"
                  (keydown.enter)="searchDieuKhoan()"
                />
              </div>

              <button
                (click)="searchDieuKhoan()"
                type="button"
                class="bo-sung-van-ban-dieu-khoan-search-button btn btn-icon btn-primary-theme"
                rippleEffect
                [disabled]="searchString == ''"
              >
                <span [data-feather]="'search'"></span>
              </button>

              <div class="col-12 mt-1 mb-1 d-flex justify-content-between">
                <div></div>
                <div class="col-12 p-0 d-flex justify-content-start mb-1">
                  <button
                    type="button"
                    class="btn btn-sm"
                    rippleEffect
                    (click)="isSearchAdvance = !isSearchAdvance; hideSearchAdvanced = false"
                    [ngClass]="{
                      'btn-primary-theme': isSearchAdvance,
                      'border-primary-theme': !isSearchAdvance
                    }"
                  >
                    <i
                      class="feather mr-25"
                      [ngClass]="
                        isSearchAdvance ? 'icon-chevrons-up' : 'icon-chevrons-down'
                      "
                    ></i>
                    {{ isSearchAdvance ? 'Thu gọn' : 'Thêm điều kiện' }}
                  </button>
                </div>
              </div>
              <div
                class="custom-md-lg-xl-search"
                [hidden]="!(isSearchAdvance && !hideSearchAdvanced)"
              >
                <div class="row w-100 m-0">
                  <!-- HÀNG 0: Địa giới / Bộ sau sáp nhập -->
                  <div class="col-12 d-flex align-items-center">
                    <div class="custom-control custom-checkbox mr-3">
                      <input
                        type="checkbox"
                        class="custom-control-input"
                        id="customCheck1"
                        [checked]="sat_nhap_tinh"
                        (change)="toggleNewDistrict($event)"
                      />
                      <label class="custom-control-label" for="customCheck1"
                        >Địa giới sau sáp nhập</label
                      >
                    </div>

                    <div class="custom-control custom-checkbox">
                      <input
                        type="checkbox"
                        class="custom-control-input cursor-pointer"
                        id="checkBoMoi"
                        [checked]="boMoi"
                        (change)="toggleBoMoi($event)"
                      />
                      <label class="custom-control-label" for="checkBoMoi"
                        >Bộ sau sáp nhập</label
                      >
                    </div>
                  </div>
                  <!-- HÀNG 1: Thể loại văn bản -->
                  <div class="col-12">
                    <div class="form-group row no-gutters align-items-center">
                      <label
                        class="label col-xxl-4 col-xl-5 col-form-label pr-2"
                        >Thể loại văn bản</label
                      >
                      <div class="col">
                        <ng-select
                          class="ng-select-size-sm"
                          multiple="true"
                          [items]="listTheLoaiVanBan"
                          bindValue="value"
                          bindLabel="label"
                          [clearable]="false"
                          placeholder="Tất cả"
                          [(ngModel)]="valueTheLoaiVanBan"
                          [ngModelOptions]="{ standalone: true }"
                          [closeOnSelect]="true"
                        >
                          <ng-template ng-option-tmp let-item="item">
                            <div>{{ item.label }}</div>
                          </ng-template>
                          <ng-template
                            ng-multi-label-tmp
                            let-items="items"
                            let-clear="clear"
                          >
                            <div
                              class="ng-value"
                              *ngFor="let item of items | slice : 0 : 2"
                            >
                              <span class="ng-value-label">{{
                                item.label
                              }}</span>
                              <span
                                class="ng-value-icon right"
                                (click)="clear(item)"
                                aria-hidden="true"
                                >×</span
                              >
                            </div>
                            <div class="ng-value" *ngIf="items.length > 2">
                              <span class="ng-value-label"
                                >{{ items.length - 2 }}
                                {{ "More" | translate }}</span
                              >
                            </div>
                          </ng-template>
                        </ng-select>
                      </div>
                    </div>
                  </div>

                  <!-- HÀNG 2: Ngày có hiệu lực -->
                  <div class="col-12 ">
                    <div class="form-group row no-gutters align-items-center">
                      <label
                        class="label col-xxl-4 col-xl-5 col-form-label pr-2"
                        >Ngày có hiệu lực</label
                      >
                      <div class="col ">
                        <ng2-flatpickr
                          [config]="customDateOptionsCoHieuLuc"
                          name="customDateCoHieuLuc"
                          placeholder="Tất cả"
                          class="flatpickr-fix"
                          (change)="valueNgayCoHieuLuc = $event?.target?.value"
                        ></ng2-flatpickr>
                        <!-- <ng-select
                          class="ng-select-size-sm"
                          multiple="true"
                          [items]="listTheLoaiVanBan"
                          bindValue="value"
                          bindLabel="label"
                          [clearable]="false"
                          placeholder="Tất cả"
                          [(ngModel)]="valueTheLoaiVanBan"
                          [ngModelOptions]="{ standalone: true }"
                          [closeOnSelect]="true"
                        >
                          <ng-template ng-option-tmp let-item="item">
                            <div>{{ item.label }}</div>
                          </ng-template>
                          <ng-template
                            ng-multi-label-tmp
                            let-items="items"
                            let-clear="clear"
                          >
                            <div
                              class="ng-value"
                              *ngFor="let item of items | slice : 0 : 2"
                            >
                              <span class="ng-value-label">{{
                                item.label
                              }}</span>
                              <span
                                class="ng-value-icon right"
                                (click)="clear(item)"
                                aria-hidden="true"
                                >×</span
                              >
                            </div>
                            <div class="ng-value" *ngIf="items.length > 2">
                              <span class="ng-value-label"
                                >{{ items.length - 2 }}
                                {{ "More" | translate }}</span
                              >
                            </div>
                          </ng-template>
                        </ng-select> -->
                      </div>
                    </div>
                  </div>

                  <!-- HÀNG 3: Ngày ban hành -->
                  <div class="col-12">
                    <div class="form-group row no-gutters align-items-center">
                      <label
                        class="label col-xxl-4 col-xl-5 col-form-label pr-2"
                        >Ngày ban hành</label
                      >
                      <div class="col">
                        <ng2-flatpickr
                          [config]="customDateOptions"
                          name="customDate"
                          class="flatpickr-fix"
                          placeholder="Tất cả"
                          (change)="changeNgayBanHanh($event)"
                        ></ng2-flatpickr>
                      </div>
                    </div>
                  </div>

                  <!-- HÀNG 4: Nơi ban hành -->
                  <div class="col-12">
                    <div class="form-group row no-gutters align-items-center">
                      <label
                        class="label col-xxl-4 col-xl-5 col-form-label pr-2"
                        >Nơi ban hành</label
                      >
                      <div class="col">
                        <ng-select
                          class="ng-select-size-sm"
                          multiple="true"
                          [items]="listCoQuanBanHanh"
                          bindValue="value"
                          bindLabel="label"
                          [clearable]="false"
                          placeholder="Tất cả"
                          (change)="selectCoQuanBanHanh($event)"
                          [closeOnSelect]="true"
                        >
                          <ng-template ng-option-tmp let-item="item">
                            <div>{{ item.label }}</div>
                          </ng-template>
                          <ng-template
                            ng-multi-label-tmp
                            let-items="items"
                            let-clear="clear"
                          >
                            <div
                              class="ng-value"
                              *ngFor="let item of items | slice : 0 : 2"
                            >
                              <span class="ng-value-label">{{
                                item.label
                              }}</span>
                              <span
                                class="ng-value-icon right"
                                (click)="clear(item)"
                                aria-hidden="true"
                                >×</span
                              >
                            </div>
                            <div class="ng-value" *ngIf="items.length > 2">
                              <span class="ng-value-label"
                                >{{ items.length - 2 }}
                                {{ "More" | translate }}</span
                              >
                            </div>
                          </ng-template>
                        </ng-select>
                      </div>
                    </div>
                  </div>

                  <!-- HÀNG 5: Tình trạng hiệu lực -->
                  <div class="col-12">
                    <div class="form-group row no-gutters align-items-center">
                      <label
                        class="label col-xxl-4 col-xl-5 col-form-label pr-2"
                        >{{ "Validity" | translate }}</label
                      >
                      <div class="col">
                        <ng-select
                          class="ng-select-size-sm"
                          multiple="true"
                          [items]="listTrangThaiHieuLuc"
                          bindValue="value"
                          bindLabel="label"
                          [clearable]="false"
                          placeholder="Tất cả"
                          (change)="selectTrangThaiHieuLuc($event)"
                          [closeOnSelect]="true"
                          [(ngModel)]="valueTrangThai"
                          [ngModelOptions]="{ standalone: true }"
                        >
                          <ng-template ng-option-tmp let-item="item">
                            <div>{{ item.label }}</div>
                          </ng-template>
                          <ng-template
                            ng-multi-label-tmp
                            let-items="items"
                            let-clear="clear"
                          >
                            <div
                              class="ng-value"
                              *ngFor="let item of items | slice : 0 : 2"
                            >
                              <span class="ng-value-label">{{
                                item.label
                              }}</span>
                              <span
                                class="ng-value-icon right"
                                (click)="clear(item)"
                                aria-hidden="true"
                                >×</span
                              >
                            </div>
                            <div class="ng-value" *ngIf="items.length > 2">
                              <span class="ng-value-label"
                                >{{ items.length - 2 }}
                                {{ "More" | translate }}</span
                              >
                            </div>
                          </ng-template>
                        </ng-select>
                      </div>
                    </div>
                  </div>

                  <!-- HÀNG 6: Nơi quản lý -->
                  <div class="col-12">
                    <div class="form-group row no-gutters align-items-center">
                      <label
                        class="label col-xxl-4 col-xl-5 col-form-label pr-2"
                        >Nơi quản lý</label
                      >
                      <div class="col">
                        <ng-select
                          class="ng-select-size-sm"
                          multiple="true"
                          [items]="listCoQuanQuanLy"
                          bindValue="value"
                          bindLabel="label"
                          [clearable]="false"
                          placeholder="Tất cả"
                          (change)="selectCoQuanQuanLy($event)"
                          [closeOnSelect]="true"
                        >
                          <ng-template ng-option-tmp let-item="item">
                            <div>{{ item.label }}</div>
                          </ng-template>
                          <ng-template
                            ng-multi-label-tmp
                            let-items="items"
                            let-clear="clear"
                          >
                            <div
                              class="ng-value"
                              *ngFor="let item of items | slice : 0 : 2"
                            >
                              <span class="ng-value-label">{{
                                item.label
                              }}</span>
                              <span
                                class="ng-value-icon right"
                                (click)="clear(item)"
                                aria-hidden="true"
                                >×</span
                              >
                            </div>
                            <div class="ng-value" *ngIf="items.length > 2">
                              <span class="ng-value-label"
                                >{{ items.length - 2 }}
                                {{ "More" | translate }}</span
                              >
                            </div>
                          </ng-template>
                        </ng-select>
                      </div>
                    </div>
                  </div>

                  <!-- HÀNG 7: Loại văn bản -->
                  <div class="col-12">
                    <div class="form-group row no-gutters align-items-center">
                      <label
                        class="label col-xxl-4 col-xl-5 col-form-label pr-2"
                        >Loại văn bản</label
                      >
                      <div class="col">
                        <ng-select
                          class="ng-select-size-sm"
                          multiple="true"
                          [items]="listLoaiVanBan"
                          bindValue="value"
                          bindLabel="label"
                          [clearable]="false"
                          placeholder="Tất cả"
                          (change)="selectLoaiVanBan($event)"
                          [closeOnSelect]="true"
                          [formControl]="type"
                        >
                          <ng-template ng-option-tmp let-item="item">
                            <div>{{ item.label }}</div>
                          </ng-template>
                          <ng-template
                            ng-multi-label-tmp
                            let-items="items"
                            let-clear="clear"
                          >
                            <div
                              class="ng-value"
                              *ngFor="let item of items | slice : 0 : 2"
                            >
                              <span class="ng-value-label">{{
                                item.label
                              }}</span>
                              <span
                                class="ng-value-icon right"
                                (click)="clear(item)"
                                aria-hidden="true"
                                >×</span
                              >
                            </div>
                            <div class="ng-value" *ngIf="items.length > 2">
                              <span class="ng-value-label"
                                >{{ items.length - 2 }}
                                {{ "More" | translate }}</span
                              >
                            </div>
                          </ng-template>
                        </ng-select>
                      </div>
                    </div>
                  </div>

                  <!-- HÀNG 8: Số lượng -->
                  <div class="col-12" id="limit">
                    <div class="form-group row no-gutters align-items-center">
                      <label
                        class="label col-xxl-4 col-xl-5 col-form-label pr-2"
                        >Số lượng</label
                      >
                      <div class="col">
                        <ng-select
                          class="ng-select-size-sm"
                          [(ngModel)]="limit"
                          [items]="sizePage"
                          [clearable]="false"
                          placeholder="Tất cả"
                          (change)="changeLimitSize($event)"
                          [ngModelOptions]="{ standalone: true }"
                        >
                        </ng-select>
                      </div>
                    </div>
                  </div>

                  <!-- HÀNG 8: Điều kiện -->
                  <div class="col-12">
                    <div class="form-group row no-gutters align-items-center">
                      <label
                        class="label col-xxl-4 col-xl-5 col-form-label pr-2"
                        ngbTooltip="Nhập các từ bạn muốn xuất hiện trong văn bản. Hệ thống sẽ ưu tiên hiển thị các văn bản có chứa những từ này, nhưng không bắt buộc tất cả đều có."
                        container="body"
                        >Điều kiện (?)</label
                      >
                      <div class="col">
                        <input
                          [(ngModel)]="should"
                          type="text"
                          class="form-control form-control-sm"
                          [ngModelOptions]="{ standalone: true }"
                          placeholder="Các văn bản có từ khóa này sẽ được hiển thị ưu tiên"
                        />
                      </div>
                    </div>
                  </div>

                  <!-- HÀNG 9: Bắt buộc -->
                  <div class="col-12">
                    <div class="form-group row no-gutters align-items-center">
                      <label
                        class="label col-xxl-4 col-xl-5 col-form-label pr-2"
                        ngbTooltip="Nhập các từ văn bản nhất định phải có. Chỉ những văn bản chứa đầy đủ các từ này mới được hiển thị trong kết quả tìm kiếm."
                        container="body"
                        >Bắt buộc (?)</label
                      >
                      <div class="col">
                        <input
                          [(ngModel)]="must"
                          type="text"
                          class="form-control form-control-sm"
                          [ngModelOptions]="{ standalone: true }"
                          placeholder="Các văn bản không từ khóa này sẽ không hiển thị"
                        />
                      </div>
                    </div>
                  </div>

                  <!-- HÀNG 10: Không -->
                  <div class="col-12">
                    <div class="form-group row no-gutters align-items-center">
                      <label
                        class="label col-xxl-4 col-xl-5 col-form-label pr-2"
                        ngbTooltip="Nhập các từ bạn muốn loại bỏ. Nếu văn bản có chứa những từ này thì sẽ bị loại khỏi kết quả tìm kiếm."
                        container="body"
                        >Không (?)</label
                      >
                      <div class="col">
                        <input
                          [(ngModel)]="not"
                          type="text"
                          class="form-control form-control-sm"
                          [ngModelOptions]="{ standalone: true }"
                          placeholder="Các văn bản có từ khóa này sẽ không hiển thị"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </form>
        </div>
      </div>
      <div *ngIf="paginatedData?.length > 0">
        <div
          class="col-12 d-flex align-items-start justify-content-between mb-1"
        >
          <div>
            <h3 class="font-weight-bolder m-0">Kết quả tìm kiếm</h3>
            <p>{{ dataTimKiemThongThuong?.length }} kết quả bao gồm :</p>
          </div>
          <div ngbDropdown class="btn-group mt-50">
            <button
              ngbDropdownToggle
              type="button"
              class="btn btn-outline-secondary"
              rippleEffect
            >
              {{ selectedSort }}
            </button>
            <div ngbDropdownMenu>
              <a
                ngbDropdownItem
                href="javascript:void(0)"
                [class.active]="selectedSort === 'Hiệu lực pháp lý'"
                (click)="selectSort('loai_van_ban')"
                >Hiệu lực pháp lý</a
              >

              <a
                ngbDropdownItem
                href="javascript:void(0)"
                [class.active]="selectedSort === 'Ngày có hiệu lực'"
                (click)="selectSort('ngay_co_hieu_luc')"
                >Ngày có hiệu lực</a
              >

              <a
                ngbDropdownItem
                href="javascript:void(0)"
                [class.active]="selectedSort === 'Tình trạng hiệu lực'"
                (click)="selectSort('tinh_trang_hieu_luc')"
                >Tình trạng hiệu lực</a
              >
              <a
                ngbDropdownItem
                href="javascript:void(0)"
                [class.active]="selectedSort === 'Tương đồng nội dung'"
                (click)="selectSort('score')"
                >Tương đồng nội dung</a
              >
            </div>
            <span
              [ngbTooltip]="
                typeSort == 'arrow-down-circle' ? 'Giảm dần' : 'Tăng dần'
              "
              placement="left"
              class="cursor-pointer d-flex align-items-center"
              (click)="loaiSapXep()"
            >
              <img
                src="assets/images/icons/desc.svg"
                alt="desc"
                *ngIf="typeSort == 'arrow-down-circle'"
              />
              <img
                src="assets/images/icons/asc.svg"
                alt="desc"
                *ngIf="typeSort != 'arrow-down-circle'"
              />
            </span>
            <span
              [ngbTooltip]="
                isShowTable ? 'Hiển thị dạng danh sách' : 'Hiển thị dạng bảng'
              "
              placement="left"
              class="d-flex align-items-center cursor-pointer"
              (click)="isShowTable = !isShowTable"
            >
              <img
                src="assets/images/icons/show-table.svg"
                alt="show-table"
                *ngIf="!isShowTable"
              />
              <img
                src="assets/images/icons/show-table-active.svg"
                alt="show-table-active"
                *ngIf="isShowTable"
              />
            </span>
            <span
              ngbTooltip="Xuất dữ liệu"
              placement="left"
              class="d-flex align-items-center cursor-pointer"
              (click)="export()"
            >
              <img
                src="assets/images/icons/export.svg"
                alt="export"
                *ngIf="dataTimKiemThongThuong.length > 0"
              />
            </span>
          </div>
        </div>

        <ng-container *ngFor="let item of statistic; let last = last">
          <span
            class="spacing"
            [ngClass]="{ active: currentLabelFilter === item.label }"
            (click)="filterResult(item.label)"
          >
            {{ item.label }} ({{ item.count }})
          </span>
        </ng-container>
      </div>
      <hr />
      <span *ngIf="isShowTable">
        <ngx-datatable
          #tableRowDetails
          [rows]="dataTimKiemThongThuongFilter"
          [rowHeight]="58"
          class="bootstrap core-bootstrap cursor"
          [columnMode]="ColumnMode.force"
          [headerHeight]="40"
          [footerHeight]="50"
          [scrollbarH]="true"
          [limit]="limitTable"
          (activate)="onActivate($event)"
          [count]="totalItem"
        >
          <ngx-datatable-row-detail [rowHeight]="'100%'">
            <ng-template
              let-row="row"
              let-expanded="expanded"
              ngx-datatable-row-detail-template
            >
              <div>
                <div
                  class="bo-sung-van-ban-dieu-khoan-detail-text ml-75 pl-5 pt-75"
                >
                  <div>
                    <span
                      ><strong>{{ "Details" | translate }}: </strong>
                      <ul>
                        <li>{{ "Domain" | translate }}: {{ row.domain }}</li>
                        <li>
                          {{ "OfficialNumber" | translate }}:
                          {{ row.so_hieu }}
                        </li>
                        <li>
                          {{ "IssuingBody" | translate }}:
                          {{ row.co_quan_ban_hanh }}
                        </li>
                        <li>
                          Tên văn bản:
                          <span [innerHTML]="row.ten_van_ban"></span>
                          <!-- {{ row.ten_van_ban }} -->
                        </li>
                      </ul>
                    </span>
                  </div>
                </div>
              </div>
            </ng-template>
          </ngx-datatable-row-detail>
          <ngx-datatable-column
            [width]="50"
            [resizeable]="false"
            [sortable]="false"
            [draggable]="false"
            [canAutoResize]="false"
          >
            <ng-template
              let-row="row"
              let-expanded="expanded"
              ngx-datatable-cell-template
            >
              <a
                href="javascript:void(0)"
                [class.datatable-icon-right]="!expanded"
                [class.datatable-icon-down]="expanded"
                title="Expand/Collapse Row"
                (click)="rowDetailsToggleExpand(row)"
              >
              </a>
            </ng-template>
          </ngx-datatable-column>
          <ngx-datatable-column
            name="Loại văn bản"
            prop="loai_van_ban"
            [width]="150"
          >
          </ngx-datatable-column>

          <ngx-datatable-column name="Trích yếu" prop="trich_yeu" [width]="500">
          </ngx-datatable-column>
          <ngx-datatable-column name="Số ký hiệu" prop="so_hieu" [width]="120">
          </ngx-datatable-column>
          <ngx-datatable-column
            name="Cơ quan ban hành"
            prop="co_quan_ban_hanh"
            [width]="150"
          >
          </ngx-datatable-column>
          <ngx-datatable-column
            *ngIf="ngay_ban_hanh"
            name="Ngày ban hành"
            prop="ngay_ban_hanh"
            [width]="150"
          >
            <ng-template
              let-status="value"
              let-row="row"
              ngx-datatable-cell-template
            >
              {{ row.ngay_ban_hanh | date : "dd-MM-yyyy" : "+0000" }}
            </ng-template>
          </ngx-datatable-column>
          <ngx-datatable-column
            name="Ngày có hiệu lực"
            prop="ngay_co_hieu_luc"
            [width]="190"
          >
            <ng-template
              let-value="value"
              let-row="row"
              ngx-datatable-cell-template
            >
              {{ value | date : "dd-MM-yyyy" : "+0000" }}
            </ng-template>
          </ngx-datatable-column>
          <ngx-datatable-column
            name="Trạng thái hiệu lực"
            prop="tinh_trang_hieu_luc"
            [width]="200"
          >
            <ng-template
              let-status="value"
              let-row="row"
              ngx-datatable-cell-template
            >
              <div
                [ngClass]="[
                  'badge',
                  status === 'Còn hiệu lực'
                    ? 'badge-light-primary'
                    : status === 'Hết hiệu lực một phần'
                    ? 'badge-light-warning'
                    : status === 'Hết hiệu lực toàn bộ'
                    ? 'badge-light-danger'
                    : 'badge-light-info'
                ]"
              >
                {{ status }}
              </div>
            </ng-template>
          </ngx-datatable-column>
        </ngx-datatable>
      </span>
      <span *ngIf="!isShowTable">
        <div class="folder h-100">
          <!-- kết quả tìm kiếm văn bản -->
          <ng-container *ngIf="!search_legal_term">
            <ng-container
              *ngFor="let item of dataTimKiemThongThuongFilter; let i = index"
            >
              <div class="d-flex align-items-center p-1 folder-item">
                <div class="bo-sung-van-ban-dieu-khoan-checkbox-container">
                  <span class="d-flex align-items-center">
                    <div class="custom-control custom-checkbox">
                      <input
                        type="checkbox"
                        class="custom-control-input checkboxSelectedFile"
                        [id]="'customCheck_' + i"
                        (change)="onCheckboxChange(item, $event)"
                        (click)="$event.stopPropagation()"
                      />
                      <label
                        class="custom-control-label"
                        [for]="'customCheck_' + i"
                        (click)="$event.stopPropagation()"
                      ></label>
                    </div>
                    <b class="m-0">
                      <span *ngIf="i < 9">0</span
                      >{{ i + (currentPage - 1) * 5 + 1 }}.&nbsp;
                    </b>
                  </span>
                </div>
                <div class="bo-sung-van-ban-dieu-khoan-content-container">
                  <div
                    class="d-flex align-items-center w-100 overflow-hidden font-weight-bolder"
                    [ngbTooltip]="item.trich_yeu | stripHtml"
                    [openDelay]="300"
                  >
                    <!-- Loại văn bản -->
                    <span class="text-primary mr-1 flex-shrink-0">
                      {{ item.loai_van_ban }}
                    </span>

                    <!-- Số hiệu -->
                    <span class="mr-1 flex-shrink-0"
                      >- {{ item.so_hieu }} -</span
                    >

                    <!-- Trích yếu -->
                    <span
                      class="text-truncate mr-1"
                      [innerHTML]="item.trich_yeu"
                      [attr.id]="'msg-' + i"
                    ></span>

                    <!-- Icon sao chép -->
                    <span
                      appcopy
                      [text]="
                        item.loai_van_ban +
                          ' - ' +
                          item.so_hieu +
                          ' - ' +
                          item.trich_yeu | stripHtml
                      "
                      class="text-primary cursor-pointer ml-1 flex-shrink-0"
                      ngbTooltip="Sao chép"
                      container="body"
                    >
                      <i data-feather="copy"></i>
                    </span>
                  </div>

                  <i
                    appExpandableText
                    *ngIf="item.toan_van != ''"
                    [text]="item?.toan_van"
                  >
                  </i>
                  <!-- để phân tách ra cho đều khoảng cách nếu không có nội dung -->
                  <div
                    *ngIf="item.toan_van == ''"
                    class="bo-sung-van-ban-dieu-khoan-empty-content"
                  ></div>
                  <div class="d-flex mt-1">
                    <p *ngIf="item.tinh_trang_hieu_luc" class="m-0">
                      Hiệu lực:
                      <span
                        class="font-weight-bolder"
                        [ngClass]="{
                          'text-danger':
                            item.tinh_trang_hieu_luc === 'Hết hiệu lực toàn bộ',
                          'text-success':
                            item.tinh_trang_hieu_luc === 'Còn hiệu lực',
                          'text-warning':
                            item.tinh_trang_hieu_luc ===
                            'Hết hiệu lực một phần',
                          'text-primary':
                            item.tinh_trang_hieu_luc === 'Chưa có hiệu lực',
                          'text-secondary':
                            item.tinh_trang_hieu_luc ===
                            'Ngưng hiệu lực một phần',
                          'text-muted':
                            item.tinh_trang_hieu_luc === 'Không còn phù hợp'
                        }"
                      >
                        {{ item.tinh_trang_hieu_luc }}
                      </span>
                    </p>
                    <p *ngIf="item.ngay_ban_hanh" class="m-0">
                      Ban hành:
                      <span class="font-weight-bolder">
                        {{ item.ngay_ban_hanh | date : "dd-MM-yyyy" : "+0000" }}
                      </span>
                    </p>
                    <p *ngIf="item.ngay_co_hieu_luc" class="m-0">
                      Áp dụng:
                      <span class="font-weight-bolder">
                        {{
                          item.ngay_co_hieu_luc | date : "dd-MM-yyyy" : "+0000"
                        }}
                      </span>
                    </p>
                  </div>
                  <!-- <div>
                      <span
                        (click)="
                          $event.stopPropagation(); viewDetailFile(action, item)
                        "
                        class="cursor-pointer action-files"
                        *ngFor="let action of actionFile; let j = index"
                        (mouseenter)="hoveredIndex[i] = j"
                        (mouseleave)="hoveredIndex[i] = null"
                        [ngClass]="{ 'text-primary': hoveredIndex[i] === j }"
                      >
                        {{ action }}
                      </span>
                    </div> -->
                </div>
              </div>
              <hr />
            </ng-container>
          </ng-container>
          <!-- kết quả tìm kiếm văn bản -->

          <!-- kết quả tìm kiếm điều khoản -->

          <ng-container *ngIf="search_legal_term">
            <ng-container
              *ngFor="let item of dataTimKiemThongThuongFilter; let i = index"
            >
              <div class="d-flex align-items-center p-1 folder-item">
                <!-- <div style="align-self: start">
                  <span class="d-flex align-items-center">
                    <b class="m-0">
                      <span *ngIf="i < 9">0</span
                      >{{ i + (currentPage - 1) * 5 + 1 }}
                    </b>
                  </span>
                </div> -->
                <div class="bo-sung-van-ban-dieu-khoan-checkbox-container">
                  <span class="d-flex align-items-center">
                    <b class="m-0">
                      <span *ngIf="i < 9">0</span
                      >{{ i + (currentPage - 1) * 5 + 1 }}.&nbsp;
                    </b>
                  </span>
                </div>
                <div class="bo-sung-van-ban-dieu-khoan-content-container">
                  <div class="wrap-text">
                    <div
                      class="d-flex align-items-center w-100 overflow-hidden font-weight-bolder"
                      [ngbTooltip]="item.trich_yeu | stripHtml"
                      [openDelay]="300"
                    >
                      <!-- Loại văn bản -->
                      <span class="text-primary flex-shrink-0">
                        {{ item.loai_van_ban }}
                      </span>

                      <!-- Số hiệu -->
                      <span class="flex-shrink-0">
                        - {{ item.so_hieu }} -
                      </span>

                      <!-- Trích yếu -->
                      <span
                        class="text-truncate"
                        [innerHTML]="item.trich_yeu"
                      ></span>

                      <!-- Icon sao chép -->
                      <span
                        appcopy
                        [text]="
                          item.loai_van_ban +
                            ' - ' +
                            item.so_hieu +
                            ' - ' +
                            item.trich_yeu | stripHtml
                        "
                        class="text-primary cursor-pointer ml-1 flex-shrink-0"
                        ngbTooltip="Sao chép"
                        container="body"
                      >
                        <i data-feather="copy"></i>
                      </span>
                    </div>

                    <!-- để phân tách ra cho đều khoảng cách nếu không có nội dung -->

                    <div class="d-flex">
                      <p *ngIf="item.tinh_trang_hieu_luc" class="m-0">
                        Hiệu lực:
                        <span
                          class="font-weight-bolder"
                          [ngClass]="{
                            'text-danger':
                              item.tinh_trang_hieu_luc ===
                              'Hết hiệu lực toàn bộ',
                            'text-success':
                              item.tinh_trang_hieu_luc === 'Còn hiệu lực',
                            'text-warning':
                              item.tinh_trang_hieu_luc ===
                              'Hết hiệu lực một phần',
                            'text-primary':
                              item.tinh_trang_hieu_luc === 'Chưa có hiệu lực',
                            'text-secondary':
                              item.tinh_trang_hieu_luc ===
                              'Ngưng hiệu lực một phần',
                            'text-muted':
                              item.tinh_trang_hieu_luc === 'Không còn phù hợp'
                          }"
                        >
                          {{ item.tinh_trang_hieu_luc }}
                        </span>
                      </p>
                      <p *ngIf="item.ngay_ban_hanh" class="m-0">
                        Ban hành:
                        <span class="font-weight-bolder">
                          {{
                            item.ngay_ban_hanh | date : "dd-MM-yyyy" : "+0000"
                          }}
                        </span>
                      </p>
                      <p *ngIf="item.ngay_co_hieu_luc" class="m-0">
                        Áp dụng:
                        <span class="font-weight-bolder">
                          {{
                            item.ngay_co_hieu_luc
                              | date : "dd-MM-yyyy" : "+0000"
                          }}
                        </span>
                      </p>
                    </div>

                    <div
                      class="text-primary"
                      (click)="$event.stopPropagation(); showTermClause(i)"
                    >
                      {{ "Điều khoản" + "(" + item.terms?.length + ")" }}
                      <img
                        [src]="
                          isShowTerm && indexTerm == i
                            ? 'assets/images/icons/up-blue.svg'
                            : 'assets/images/icons/down-blue.svg'
                        "
                        alt="chevron"
                      />
                    </div>
                    <ng-container *ngIf="isShowTerm && i == indexTerm">
                      <ng-container
                        *ngFor="let term of item.terms; let j = index"
                      >
                        <div class="collapse-icon" id="detail-term">
                          <ngb-accordion
                            #acc="ngbAccordion"
                            (click)="$event.stopPropagation()"
                          >
                            <ngb-panel>
                              <ng-template ngbPanelTitle>
                                <span
                                  class="bo-sung-van-ban-dieu-khoan-accordion-title lead collapse-title card-title d-flex align-items-center font-sm"
                                >
                                  <div
                                    class="custom-control custom-checkbox mr-2"
                                  >
                                    <input
                                      type="checkbox"
                                      class="custom-control-input checkboxSelectedFile"
                                      [id]="'customCheck_2' + j"
                                      (change)="onCheckboxChange(term, $event)"
                                      (click)="$event.stopPropagation()"
                                    />
                                    <label
                                      class="custom-control-label"
                                      [for]="'customCheck_2' + j"
                                      (click)="$event.stopPropagation()"
                                    ></label>
                                  </div>
                                  <p
                                    class="m-0 one-line"
                                    [ngbTooltip]="term.term_title"
                                    container="body"
                                  >
                                    {{ term.position }} - {{ term.term_title }}
                                  </p>
                                  <span
                                    appcopy
                                    class="text-primary cursor-pointer mr-1 flex-shrink-0"
                                    [text]="
                                      term.position + ' - ' + term.term_title
                                    "
                                    ngbTooltip="Sao chép"
                                    container="body"
                                  >
                                    <i data-feather="copy"></i>
                                  </span>
                                </span>
                              </ng-template>
                              <ng-template ngbPanelContent>
                                <p
                                  appExpandableText
                                  [text]="
                                    term.raw_content
                                      ? term.raw_content
                                      : term.term_content
                                  "
                                ></p>
                              </ng-template>
                            </ngb-panel>
                          </ngb-accordion>
                        </div>
                        <hr />
                      </ng-container>
                    </ng-container>
                  </div>
                </div>
              </div>
              <hr />
            </ng-container>
          </ng-container>
          <!-- tìm kiếm điều khoản -->
        </div>
        <ng-container *ngIf="noData">
          <div class="mt-1"></div>
          <app-no-data></app-no-data>
        </ng-container>
      </span>
    </section>
    <div class="save-file" *ngIf="selectedFiles.length > 0">
      <div
        class="selection-bar p-1 d-flex align-items-center justify-content-between"
      >
        <!-- Nội dung -->
        <span class="selected-text"
          >Đã chọn {{ selectedFiles.length }}
          {{ search_legal_term ? "điều khoản" : "văn bản" }}
        </span>
        <span class="bo-sung-van-ban-dieu-khoan-separator mx-1"></span>
        <!-- Nút lưu tài liệu -->
        <button
          class="save-btn d-flex align-items-center"
          (click)="saveHistoryFiles()"
        >
          <img src="assets/images/icons/folder-star.svg" alt="folder-star" />
          <span class="ms-2"
            >Thêm {{ search_legal_term ? "điều khoản" : "văn bản" }}</span
          >
        </button>
      </div>
    </div>
  </div>
</core-card>
