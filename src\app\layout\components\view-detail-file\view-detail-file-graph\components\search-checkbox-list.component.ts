import { Component, Input, Output, EventEmitter, OnInit, OnChanges, SimpleChanges } from '@angular/core';

/**
 * Reusable component for search bar with checkbox list
 * Used for filtering options in accordion panels
 */
@Component({
  selector: 'app-search-checkbox-list',
  templateUrl: './search-checkbox-list.component.html',
  styleUrls: ['./search-checkbox-list.component.scss']
})
export class SearchCheckboxListComponent implements OnInit, OnChanges {
  /**
   * List of all available options to display
   */
  @Input() options: Array<{ label: string; value: any }> = [];
  
  /**
   * List of currently selected values
   */
  @Input() selectedValues: any[] = [];
  
  /**
   * Placeholder text for search input
   */
  @Input() searchPlaceholder: string = 'Tìm kiếm';
  
  /**
   * Unique ID prefix for checkbox elements
   */
  @Input() idPrefix: string = 'checkbox';
  
  /**
   * Maximum height for the checkbox list (in pixels)
   */
  @Input() maxHeight?: number;
  
  /**
   * Optional color map for rendering color dots (keyed by option value/label)
   */
  @Input() colorMap: Record<string, string> = {};
  
  /**
   * Event emitted when selection changes
   */
  @Output() selectionChange = new EventEmitter<any[]>();
  
  /**
   * Current search term
   */
  searchTerm: string = '';
  
  /**
   * Filtered options based on search
   */
  filteredOptions: Array<{ label: string; value: any }> = [];
  
  ngOnInit(): void {
    this.updateFilteredOptions();
  }

  ngOnChanges(changes: SimpleChanges): void {
    // When the options list changes (e.g., after hide/restore nodes),
    // re-apply the current search term to update the rendered list.
    if (changes['options']) {
      this.updateFilteredOptions();
    }
  }
  
  /**
   * Update filtered options when search changes
   */
  onSearchChange(): void {
    this.updateFilteredOptions();
  }
  
  /**
   * Filter options based on search term
   */
  private updateFilteredOptions(): void {
    const searchLower = this.searchTerm.toLowerCase().trim();
    if (!searchLower) {
      this.filteredOptions = [...this.options];
    } else {
      this.filteredOptions = this.options.filter(option =>
        option.label.toLowerCase().includes(searchLower)
      );
    }
  }
  
  /**
   * Check if value is selected
   */
  isSelected(value: any): boolean {
    return this.selectedValues.includes(value);
  }
  
  /**
   * Toggle selection of a value
   */
  toggleSelection(value: any): void {
    const index = this.selectedValues.indexOf(value);
    const newSelection = [...this.selectedValues];
    
    if (index > -1) {
      newSelection.splice(index, 1);
    } else {
      newSelection.push(value);
    }
    
    this.selectionChange.emit(newSelection);
  }

  /**
   * Resolve color for an option (checks value then label)
   */
  getColor(option: { label: string; value: any }): string {
    return this.colorMap[option.value] || this.colorMap[option.label] || '';
  }
}

