<div class="clsc-chat">
  <!-- SIDEBAR LỊCH SỬ -->
  <aside class="clsc-chat__sidebar">
    <div class="clsc-chat__sidebar-header">
      <button class="clsc-chat__new-btn" (click)="startNewSession()">
        Chat mới
      </button>
    </div>

    <div class="clsc-chat__sidebar-section">
      <div class="clsc-chat__sidebar-title">L<PERSON><PERSON> sử</div>

      <button
        *ngFor="let s of sessions"
        class="clsc-chat__session"
        [class.clsc-chat__session--active]="s.id === activeSessionId"
        (click)="selectSession(s.id)"
      >
        <div class="clsc-chat__session-row">
          <div class="clsc-chat__session-text">
            <!-- <PERSON><PERSON> rename -->
            <ng-container *ngIf="editingSessionId === s.id; else viewTitle">
              <input
                #titleInput
                id="session-input-{{ s.id }}"
                class="clsc-chat__session-input"
                [(ngModel)]="editTitle"
                (keydown.enter)="confirmRename(s)"
                (blur)="confirmRename(s)"
              />
              <div class="clsc-chat__session-date">
                {{ s.date }}
              </div>
            </ng-container>

            <!-- Hiển thị bình thường -->
            <ng-template #viewTitle>
              <div class="clsc-chat__session-name">{{ s.title }}</div>
              <div class="clsc-chat__session-date">{{ s.date }}</div>
            </ng-template>
          </div>

          <!-- Nút sửa tên -->
          <button
            class="clsc-chat__session-rename"
            type="button"
            (click)="startRename(s, $event)"
          >
            <i data-feather="edit-2"></i>
          </button>
        </div>
      </button>
    </div>

    <!-- ĐÃ BỎ FOOTER USER Ở ĐÂY -->
  </aside>

  <!-- MAIN CHAT AREA -->
  <section class="clsc-chat__main" *ngIf="activeSession as session">
    <!-- HEADER -->
    <header class="clsc-chat__header">
      <div class="clsc-chat__header-left">
        <h2 class="clsc-chat__title">{{ session.title }}</h2>
        <p class="clsc-chat__subtitle">
          C-LegalLM Pro
        </p>
      </div>

      <!-- BỎ “Về trang Landing” -->
      <!--
      <div class="clsc-chat__header-right">
        <a routerLink="/home" class="clsc-chat__link">Về trang Landing</a>
      </div>
      -->
    </header>

    <!-- TOOLBAR -->
    <!-- <div class="clsc-chat__toolbar">
      <button
        class="clsc-chip"
        [class.clsc-chip--active]="useRepo"
        (click)="useRepo = !useRepo"
      >
        Kho dữ liệu
      </button>

      <button
        class="clsc-chip clsc-chip--green"
        [class.clsc-chip--active]="useGoogle"
        (click)="useGoogle = !useGoogle"
      >
        Google Search
      </button>

      <button
        class="clsc-chip clsc-chip--red"
        [class.clsc-chip--active]="modelMode === 'auto'"
        (click)="modelMode = modelMode === 'auto' ? 'c-legal' : 'auto'"
      >
        {{ modelMode === "auto" ? "Tự động" : "C-LegalLM Pro" }}
      </button>

      <div class="clsc-chat__toolbar-spacer"></div>

      <div class="clsc-chat__role">
        <span class="clsc-chat__role-label">Vai trò:</span>
        <select [(ngModel)]="selectedRole" class="clsc-chat__role-select">
          <option *ngFor="let r of roles" [value]="r">
            {{ r }}
          </option>
        </select>
      </div>
    </div> -->

    <!-- BODY -->
    <div class="clsc-chat__body">
      <ng-container *ngIf="session.messages.length === 1; else hasMessages">
        <div class="clsc-chat__empty">
          <!-- <div class="clsc-chat__empty-icon">⚖️</div> -->
          <h3 class="clsc-chat__empty-title">C-LegalLM Pro</h3>
          <p class="clsc-chat__empty-desc">
            Trợ lý pháp lý thông minh, hỗ trợ tra cứu đa nguồn và thẩm định rủi
            ro pháp lý cho phòng Pháp chế / Ban Điều hành.
          </p>

          <div class="clsc-chat__suggest-grid">
            <button
              class="clsc-suggest"
              *ngFor="let q of quickSuggestions"
              (click)="sendFromSuggestion(q)"
            >
              <div class="clsc-suggest__icon">💡</div>
              <div class="clsc-suggest__text">{{ q }}</div>
              <div class="clsc-suggest__tag">Đa nguồn</div>
            </button>
          </div>
        </div>
        <div class="clsc-chat__thinking" *ngIf="thinkingStatus">
          {{ thinkingStatus }}
        </div>
      </ng-container>

      <ng-template #hasMessages>
        <div class="clsc-chat__messages">
          <div
            *ngFor="let msg of session.messages"
            class="clsc-msg"
            [class.clsc-msg--user]="msg.role === 'user'"
          >
          <div class="clsc-msg__avatar">
            {{ msg.role === "user" ? userInitials : "AI" }}
          </div>
            <div class="clsc-msg__content">
              <div class="clsc-msg__bubble">
                <!-- thinking inline cho bubble đang stream -->
                <div
                  *ngIf="
                    isStreaming &&
                    thinkingStatus &&
                    msg.role === 'assistant' &&
                    msg === session.messages[session.messages.length - 1]
                  "
                  class="clsc-msg__thinking-inline"
                >
                  {{ thinkingStatus }}
                </div>

                <div
                  class="clsc-msg__markdown"
                  [innerHTML]="renderMessageContent(msg.content)"
                ></div>
              </div>

              <div
                class="clsc-msg__sources"
                *ngIf="msg.sources?.length"
              >
                <div class="clsc-msg__sources-label">
                  Nguồn tham khảo:
                </div>
                <div class="clsc-msg__sources-list">
                  <button
                    *ngFor="let s of msg.sources"
                    class="clsc-source-pill"
                  >
                    <span class="clsc-source-pill__dot"></span>
                    <span class="clsc-source-pill__title">{{ s.title }}</span>
                    <span *ngIf="s.page" class="clsc-source-pill__page">
                      {{ s.page }}
                    </span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </ng-template>
    </div>

    <!-- FOLLOW UP -->
    <div class="clsc-chat__followups" *ngIf="followUps.length">
      <button
        class="clsc-followup"
        *ngFor="let f of followUps"
        (click)="sendFromFollowUp(f)"
      >
        {{ f }}
      </button>
    </div>

    <!-- INPUT BAR -->
    <div class="clsc-chat__input">
    <div class="clsc-chat__input-inner">
        <button class="clsc-input__addon">+</button>

        <textarea
        class="clsc-input__textarea"
        [(ngModel)]="inputValue"
        placeholder="Nhập câu hỏi pháp lý..."
        rows="1"
        (keydown)="handleKeyDown($event)"
        ></textarea>

        <button
        class="clsc-input__send"
        [disabled]="!inputValue.trim()"
        (click)="sendMessage()"
        >
        ➤
        </button>
    </div>
    </div>

  </section>
</div>
