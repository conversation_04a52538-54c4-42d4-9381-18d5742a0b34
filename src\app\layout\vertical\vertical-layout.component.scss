@import '@core/scss/base/core/menu/menu-types/vertical-menu.scss';
@import '@core/scss/base/core/menu/menu-types/vertical-overlay-menu.scss';

// .app-content.vertical-layout-app-content {
//   height: 93vh !important;
//   padding-top: 7vh !important;
// }

.vertical-layout-admin-margin {
  margin-top: 1rem;
}

.vertical-layout-navbar-admin {
  box-shadow: 0 4px 24px 0 rgba(0, 0, 0, 0.1);
  border-bottom: none;
  border-radius: 5px;
}

.app-content.commercial-content {
  padding-top: 0 !important;
  margin-top: 0 !important;
}

.app-content.commercial-content .content-wrapper {
  padding-top: 0 !important;
}

.app-content.commercial-content .content-header {
  margin-top: 0 !important;
}

.vertical-layout.menu-expanded .app-content.commercial-content,
.vertical-layout .app-content.commercial-content {
  margin-left: 0 !important;
  width: 100% !important;
}

.vertical-layout .app-content.commercial-content .content-wrapper {
  margin-left: 0 !important;
  padding-left: 0;
}