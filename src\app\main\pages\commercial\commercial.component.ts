import {
  Compo<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>t,
  AfterViewInit
} from "@angular/core";
import {
  trigger,
  transition,
  style,
  animate
} from "@angular/animations";
import { Subject } from "rxjs";
import { takeUntil } from "rxjs/operators";
import { AuthenticationService } from "app/auth/service";
import { Router } from "@angular/router";

declare const feather: any;

type CommercialTab = "search" | "chat";

interface NavItem {
  id: CommercialTab;
  label: string;
  icon: string;
}

@Component({
  selector: "app-commercial",
  templateUrl: "./commercial.component.html",
  styleUrls: ["./commercial.component.scss"],
  animations: [
    trigger("tabFadeSlide", [
      transition(":enter", [
        style({
          opacity: 0,
          transform: "translateY(12px)"
        }),
        animate(
          "250ms ease-out",
          style({
            opacity: 1,
            transform: "translateY(0)"
          })
        )
      ]),
      transition(":leave", [
        style({
          opacity: 1,
          transform: "translateY(0)"
        }),
        animate(
          "150ms ease-in",
          style({
            opacity: 0,
            transform: "translateY(-8px)"
          })
        )
      ])
    ])
  ]
})
export class CommercialComponent implements OnInit, OnDestroy, AfterViewInit {
  // activeTab: CommercialTab = "search";
  activeTab: CommercialTab = "chat";
  isSidebarCollapsed = false;

  // Sidebar menu – chỉ 2 mục tạm thời
  navItems: NavItem[] = [
    {
      id: "search",
      label: "Tìm kiếm",
      icon: "search" // feather icon: search
    },
    {
      id: "chat",
      label: "Trợ lý Hỏi đáp",
      icon: "message-square" // feather icon: message-square
    }
    // Sau này muốn mở rộng thì thêm:
    // { id: "repo", label: "Kho dữ liệu", icon: "database" },
    // { id: "compare", label: "So sánh văn bản", icon: "file-text" },
    // { id: "draft", label: "Soạn thảo", icon: "file" },
    // { id: "review", label: "Thẩm định", icon: "check-circle" },
    // { id: "prompt", label: "Cấu hình Prompt", icon: "sliders" },
  ];

  currentUser: any;
  userName = "";
  userInitials = "U";
  private _destroy$ = new Subject<void>();

  constructor(
    private authService: AuthenticationService,
    private router: Router
  ) {
    this.authService.currentUser
      .pipe(takeUntil(this._destroy$))
      .subscribe((user) => {
        this.currentUser = user;
        this.userName = user?.fullname || "CLS User";
        this.userInitials = this.buildInitials(this.userName);
      });
  }

  ngOnInit(): void {}

  ngAfterViewInit(): void {
    this.refreshFeatherIcons();
  }

  private refreshFeatherIcons(): void {
    try {
      if (typeof feather !== "undefined" && feather.replace) {
        feather.replace();
      }
    } catch (e) {
      // tránh crash nếu feather chưa load
      // console.warn('Feather not available', e);
    }
  }

  setActiveTab(tab: CommercialTab): void {
    this.activeTab = tab;
    // Nếu anh muốn mỗi lần đổi tab cũng refresh icon (thường không cần)
    this.refreshFeatherIcons();
  }

  toggleSidebar(): void {
    this.isSidebarCollapsed = !this.isSidebarCollapsed;
    // DOM sidebar chỉ đổi class, icon vẫn giữ nguyên nên không bắt buộc refresh
  }

  ngOnDestroy(): void {
    this._destroy$.next();
    this._destroy$.complete();
  }

  private buildInitials(name: string): string {
    if (!name) return "U";
    return name
      .trim()
      .split(/\s+/)
      .slice(-2) // lấy 1–2 từ cuối
      .map((p) => p[0])
      .join("")
      .toUpperCase();
  }

  logout() {
    this.authService.logout();
    this.router.navigate(["/pages/authentication/login-v2"]);
  }
}
