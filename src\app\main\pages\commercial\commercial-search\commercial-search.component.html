<div class="commercial-search">
  <!-- Cột lọc loại văn bản (bên tr<PERSON>i) -->
  <!-- <aside class="search-filter d-none d-md-block">
    <div class="filter-header d-flex justify-content-between align-items-center">
      <h3 class="filter-title mb-0">Lo<PERSON><PERSON> văn bản</h3>
      <i data-feather="chevron-up" class="filter-collapse-icon"></i>
    </div>

    <div class="filter-body mt-3">
      <div
        class="filter-item"
        *ngFor="let t of docTypes"
      >
        <label class="filter-checkbox mb-0">
          <input
            type="checkbox"
            [checked]="t.checked"
            (change)="toggleDocType(t, $event)"
          />
          <span class="filter-label">
            {{ t.label }}
            <span class="filter-count">({{ t.count | number }})</span>
          </span>
        </label>
      </div>
    </div>
  </aside> -->

  <!-- <PERSON><PERSON>n nội dung chính -->
  <section class="search-main">
    <!-- Tiêu đề + nút -->
    <div class="search-main-header d-flex justify-content-between align-items-start">
      <div>
      <!-- <h2 class="search-title mb-1">Kho dữ liệu tập trung</h2>

      <p
        class="search-subtitle mb-0"
        *ngIf="!hasSearched"
      >
        Quản lý tập trung văn bản pháp lý và tài liệu nội bộ của tổ chức. 
      </p>

      <ng-container *ngIf="hasSearched">
        <p
          class="search-subtitle mb-0"
          *ngIf="isLoading"
        >
          Đang tìm kiếm tài liệu phù hợp với từ khóa "{{ searchKeyword }}".
        </p>

        <p
          class="search-subtitle mb-0"
          *ngIf="!isLoading"
        >
          Tìm thấy {{ totalResults | number }} tài liệu phù hợp với từ khóa
          "{{ searchKeyword }}".
        </p>
      </ng-container> -->
        <h2 class="search-title mb-1">Kho dữ liệu tập trung</h2>
        <p class="search-subtitle mb-0">
          Quản lý tập trung văn bản pháp lý và tài liệu nội bộ của tổ chức.
        </p>

      </div>

      <!-- nút Upload chỉ hiện ở tab Dữ liệu cá nhân (mock giao diện) -->
      <button
        *ngIf="activeTab === 'personal'"
        type="button"
        class="btn btn-primary search-upload-btn"
      >
        <i data-feather="upload" class="me-1"></i>
        Tải lên
      </button>
    </div>

    <!-- Ô tìm kiếm -->
    <div class="search-input-wrapper mt-3 mb-3">
      <div
        class="search-input-inner d-flex align-items-center"
        [class.search-input-focused]="searchKeyword"
      >
        <i data-feather="search" class="search-icon"></i>
        <input
          class="form-control search-input"
          type="text"
          [(ngModel)]="searchKeyword"
          (keyup.enter)="onSubmitSearch()"
          placeholder="Tìm kiếm theo tên, loại hoặc ngày..."
        />
        <button
          type="button"
          class="btn btn-sm ms-2 search-btn"
          (click)="onSubmitSearch()"
        >
          Tìm kiếm
        </button>
      </div>
    </div>

    <!-- Tabs -->
    <div class="search-tabs d-flex align-items-center">
      <button
        type="button"
        class="search-tab"
        [class.search-tab-active]="activeTab === 'org'"
        (click)="changeTab('org')"
      >
        Dữ liệu Tổ chức
      </button>
      <!-- <button
        type="button"
        class="search-tab"
        [class.search-tab-active]="activeTab === 'personal'"
        (click)="changeTab('personal')"
      >
        Dữ liệu Cá nhân
      </button> -->
    </div>

    <div *ngIf="hasSearched" class="search-result-info mt-3">
      <span *ngIf="!isLoading">
        Tìm thấy
        <strong>{{ totalResults | number }}</strong>
        kết quả cho
        "<strong>{{ searchKeyword }}</strong>"
      </span>

      <span *ngIf="isLoading">
        Đang tìm kiếm cho
        "<strong>{{ searchKeyword }}</strong>"
      </span>

      <!-- <button type="button" class="btn-link-clear" (click)="clearSearch()">
        Xóa lọc
      </button> -->
    </div>

    <!-- Trạng thái BAN ĐẦU: chưa tìm kiếm -->
    <div
      *ngIf="!hasSearched && !isLoading && files.length === 0"
      class="search-empty"
    >
      <div class="search-empty-icon">
        <i data-feather="search"></i>
      </div>
      <h4 class="mb-1">Tìm kiếm văn bản pháp luật</h4>
      <p class="mb-0">
        Nhập từ khóa (số hiệu, trích yếu, loại văn bản, ngày ban hành…) để tra cứu
        văn bản pháp luật và tài liệu nội bộ trong kho dữ liệu tập trung.
      </p>
    </div>

    <!-- Trạng thái ĐÃ TÌM nhưng không có kết quả -->
    <div
      *ngIf="hasSearched && files.length === 0"
      class="search-empty"
    >
      <!-- Đang loading -->
      <ng-container *ngIf="isLoading; else emptyState">
        <div class="search-empty-icon">
          <span class="spinner-border" role="status">
            <span class="sr-only">Loading...</span>
          </span>
        </div>
        <h4 class="mb-1">Đang tìm kiếm...</h4>
        <p class="mb-0">Vui lòng đợi trong giây lát.</p>
      </ng-container>

      <!-- Không có kết quả -->
      <ng-template #emptyState>
        <div class="search-empty-icon">
          <i data-feather="search"></i>
        </div>
        <h4 class="mb-1">Không tìm thấy kết quả</h4>
        <p class="mb-0">
          Không có tài liệu nào phù hợp với từ khóa
          "<strong>{{ searchKeyword }}</strong>".
        </p>
      </ng-template>
    </div>


    <!-- Bảng kết quả -->
    <div
      *ngIf="files.length > 0"
      class="search-table-wrapper mt-3"
    >
      <div class="search-table-header d-flex align-items-center">
        <div class="col-name flex-grow-1">TÊN TÀI LIỆU</div>
        <div class="col-type">LOẠI</div>
        <!-- <div class="col-size">DUNG LƯỢNG</div> -->
        <div class="col-date">NGÀY TẢI LÊN</div>
        <!-- <div class="col-action text-center">HÀNH ĐỘNG</div> -->
      </div>

      <div class="search-table-body">
        <div
          *ngFor="let file of pagedFiles"
          class="search-row d-flex align-items-center"
          (click)="viewFile(file)"
        >
          <!-- tên file -->
          <div class="col-name flex-grow-1 d-flex align-items-center">
            <div class="file-icon">
              <i data-feather="file-text"></i>
            </div>
            <span class="file-name text-truncate">
              {{ file.name }}
            </span>
          </div>

          <!-- loại -->
          <div class="col-type">
            <span class="tag-pill">
              {{ file.tag }}
            </span>
          </div>

          <!-- size -->
          <!-- <div class="col-size">
            {{ file.size }}
          </div> -->

          <!-- date -->
          <div class="col-date">
            {{ file.date }}
          </div>

          <!-- actions -->
          <!-- <div class="col-action text-center">
            <button
              type="button"
              class="btn-icon"
              (click)="viewFile(file); $event.stopPropagation()"
            >
              <i data-feather="eye"></i>
            </button>
          </div> -->
        </div>
      </div>

    </div>
    <div
      *ngIf="files.length > 0 && totalPages > 1"
      class="search-pagination d-flex justify-content-between align-items-center"
    >
      <span class="pagination-info">
        Hiển thị
        {{ (currentPage - 1) * pageSize + 1 }}
        –
        {{ (currentPage - 1) * pageSize + pagedFiles.length }}
        trong số
        {{ files.length | number }}
        kết quả
      </span>

      <div class="pagination-buttons">
        <!-- Tới trang đầu -->
        <button
          type="button"
          class="btn-page btn-page-icon"
          [disabled]="currentPage === 1"
          (click)="gotoPage(1)"
        >
          <i data-feather="chevrons-left"></i>
        </button>

        <!-- Lùi 1 trang -->
        <button
          type="button"
          class="btn-page btn-page-icon"
          [disabled]="currentPage === 1"
          (click)="gotoPage(currentPage - 1)"
        >
          <i data-feather="chevron-left"></i>
        </button>

        <!-- Các số trang -->
        <button
          type="button"
          class="btn-page"
          *ngFor="let page of visiblePages"
          [class.btn-page-active]="currentPage === page"
          (click)="gotoPage(page)"
        >
          {{ page }}
        </button>

        <!-- Tới 1 trang -->
        <button
          type="button"
          class="btn-page btn-page-icon"
          [disabled]="currentPage === totalPages"
          (click)="gotoPage(currentPage + 1)"
        >
          <i data-feather="chevron-right"></i>
        </button>

        <!-- Tới trang cuối -->
        <button
          type="button"
          class="btn-page btn-page-icon"
          [disabled]="currentPage === totalPages"
          (click)="gotoPage(totalPages)"
        >
          <i data-feather="chevrons-right"></i>
        </button>
      </div>
    </div>

  </section>
  <ng-template #viewFileModal let-modal>
    <app-view-detail-file
      [modal]="modal"
      [type]="FormType.Search"
      [hideChartTab]="true"
      [hideSaveButton]="true"
      [hideBackButton]="true"
      [showCloseIcon]="true"  
      [showCloseButton]="true"
    >
    </app-view-detail-file>
  </ng-template>
</div>
