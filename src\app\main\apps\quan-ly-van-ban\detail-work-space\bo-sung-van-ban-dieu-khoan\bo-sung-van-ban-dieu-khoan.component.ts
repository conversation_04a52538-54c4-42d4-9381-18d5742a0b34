import { Component, Input, OnInit, ViewChild } from "@angular/core";
import { FormControl } from "@angular/forms";
import { ActivatedRoute } from "@angular/router";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { PaginationService } from "@core/components/paginate/paginatioin.service";
import { ColumnMode, DatatableComponent } from "@swimlane/ngx-datatable";
import { CacheDataService } from "app/auth/service/cache-data.service";
import { ViewDetailFileService } from "app/layout/components/view-detail-file/view-detail-file.service";
import { DocumentStatus } from "app/models/DocumentStatus";
import { ShowSideBar } from "app/models/ShowSideBa";
import { FlatpickrOptions } from "ng2-flatpickr";
import { ToastrService } from "ngx-toastr";
import { Subject } from "rxjs";
import { takeUntil } from "rxjs/operators";
import { ComnpareClauseChatbotService } from "../compare-clause-chatbot/comnpare-clause-chatbot.service";
import { DetailClauseService } from "../detail-clause/detail-clause.service";
import { DetailWorkSpaceService } from "../detail-work-space.service";
import { ListDocumentService } from "../list-document/list-document.service";
import { BoSungVanBanDieuKhoanService } from "./bo-sung-van-ban-dieu-khoan.service";
import { LoaiVanBan } from "app/models/LoaiVanBan";
import { TrangThaiHieuLuc } from "app/models/TrangThaiHieuLuc";
import { CoQuanBanHanhTinhCuBoCu } from "app/models/CoQuanBanHanhTinhCu";
import { CoQuanBanHanhTinhMoiBoCu } from "app/models/CoQuanBanHanhMoiTinhMoi";
import { CoQuanBanHanhTinhCuBoMoi } from "app/models/CoQuanBanHanhTinhCuBoMoi";
import { CoQuanBanHanhTinhMoiBoMoi } from "app/models/CoQuanBanHanhTinhMoiBoMoi";
import { CoQuanQuanLyTinhCuBoCu } from "app/models/CoQuanQuanLyTinhCuBoCu";
import { CoQuanQuanLyTinhMoiBoCu } from "app/models/CoQuanQuanLyTinhMoiBoCu";
import { CoQuanQuanLyTinhCuBoMoi } from "app/models/CoQuanQuanLyTinhCuBoMoi";
import { CoQuanQuanLyTinhMoiBoMoi } from "app/models/CoQuanQuanLyTinhMoiBoMoi";
@Component({
  selector: "app-bo-sung-van-ban-dieu-khoan",
  templateUrl: "./bo-sung-van-ban-dieu-khoan.component.html",
  styleUrls: ["./bo-sung-van-ban-dieu-khoan.component.scss"],
})
export class BoSungVanBanDieuKhoanComponent implements OnInit {
  @ViewChild("tableRowDetails") tableRowDetails: DatatableComponent;
  @ViewChild(DatatableComponent) table: DatatableComponent;
  @Input() isInModal: boolean = false; // Để kiểm tra xem component đang ở modal hay sidebar

  public sizePage = [100, 300, 500, 1000];
  public isSearchAdvance: boolean = false;
  public page: number = 1;
  public totalItem: number = 0;
  public ColumnMode = ColumnMode;
  public limit: number = this.sizePage[0];
  public limitTable = 8;
  public dataTimKiemThongThuong: any = [];
  public dataTimKiemThongThuongFilter: any = [];
  public statistic: any = [];
  public searchString: string;
  public filterString: string;
  public dataExport: any;
  public typeDoiChieu: any;
  public listId: [] = [];
  public valueTrangThai: any = ["Còn hiệu lực", "Hết hiệu lực một phần"];
  public valueLoaiVanBan: [] = [];
  public type: FormControl = new FormControl([]);
  public valueCoQuanBanHanh: [] = [];
  public valueCoQuanQuanLy: [] = [];
  public listLoaiVanBan: any = LoaiVanBan;
  public listCoQuanBanHanh: any = CoQuanBanHanhTinhCuBoCu;
  public listTrangThaiHieuLuc: any = TrangThaiHieuLuc;
  public listCoQuanQuanLy: any = CoQuanQuanLyTinhCuBoCu;

  listDoiChieu: Array<{ label: string; value: string }> = [];

  public pageDefault: number = 1;
  isShowTable: boolean = false;
  public currentPage = 1;
  public itemsPerPage = 5;
  public search_legal_term: boolean = true;
  public filterBody = { sort: "score", order: "desc" };
  selectedSort: string = "Sắp xếp";
  public typeSort: string = "arrow-down-circle";
  public noData: boolean = false;
  public valueNgayBanHanh: string = null;
  public valueNgayCoHieuLuc: string = null;
  public paramSearch: any;
  public customDateOptions: FlatpickrOptions = {
    altFormat: "j-m-Y",
    enableTime: false,
    altInput: true,
    mode: "range",
    inputClass: "form-control form-control-sm w-100",
  };
  public customDateOptionsCoHieuLuc: FlatpickrOptions = {
    altFormat: "j-m-Y",
    enableTime: false,
    altInput: true,
    mode: "range",
    inputClass: "form-control form-control-sm w-100",
  };

  public isReload = false;
  hoveredIndex: { [key: number]: number | null } = {};
  searchQuery: string = "";
  searchResults: any[] = [];
  public must: string = "";
  public should: string = "";
  public not: string = "";
  public currentLabelFilter: string = "Tất cả";
  public unSubAll: Subject<any> = new Subject();
  public selectedFiles: any[] = [];
  public DocumentStatus = DocumentStatus;
  public hideSearchAdvanced: boolean = false;
  public idClauseParent: string = ""; // id đang được chọn từ danh sách điều khảon trong danh sáhc tài liệu
  public fileId: string;
  public typeAddFile: string = "";
  public indexTerm: number = -1;
  public isShowTerm: boolean = false;
  public sat_nhap_tinh: boolean = false;
  public boMoi: boolean = false;
  public listTheLoaiVanBan = [
    { label: "Văn bản hành chính", value: "văn bản hành chính" },
    { label: "Văn bản quy phạm", value: "văn bản quy phạm" },
  ];
  public valueTheLoaiVanBan: any[] = [];
  constructor(
    private paginationService: PaginationService,
    private toast: ToastrService,
    public cacheDataService: CacheDataService,
    private viewDetailFileService: ViewDetailFileService,
    private workspace: DetailWorkSpaceService,
    private listDocumentService: ListDocumentService,
    private boSungDieuKhoanService: BoSungVanBanDieuKhoanService,
    private compareChatbotService: ComnpareClauseChatbotService,
    private detailClauseService: DetailClauseService,
    private route: ActivatedRoute,
    private modalService: NgbModal
  ) {}

  ngOnInit(): void {
    this.searchString = "";
    this.filterString = "";
    this.dataTimKiemThongThuong = [];
    this.statistic = [];
    this.dataExport = [];
    this.route.queryParams
      .pipe(takeUntil(this.unSubAll))
      .subscribe((params) => {
        this.fileId = params["fileId"];
      });

    // Khởi tạo danh sách cơ quan theo trạng thái sáp nhập / bộ
    this.updateLists();

    this.search_legal_term = this.boSungDieuKhoanService.typeSearch.value;
    this.boSungDieuKhoanService.typeAddFile
      .pipe(takeUntil(this.unSubAll))
      .subscribe((res) => {
        this.typeAddFile = res;
      });

    this.compareChatbotService.clauseInfo1
      .pipe(takeUntil(this.unSubAll))
      .subscribe((res) => {
        this.idClauseParent = res.id;
      });
    this.listDocumentService.FileSearchTemp.next(null); // để xoá file Term đang tìm kiếm bên danh sahsc tài liệu
  }
  rowDetailsToggleExpand(row) {
    this.tableRowDetails.rowDetail.toggleExpandRow(row);
  }

  getLoaiVanBan() {
    this.boSungDieuKhoanService.getLoaiVanBan().subscribe((res) => {
      this.listLoaiVanBan = res;
    });
  }
  getCoQuanBanHanh() {
    this.boSungDieuKhoanService.getCoQuanBanHanh().subscribe((res) => {
      this.listCoQuanBanHanh = res;
    });
  }
  getTrangThaiHieuLuc() {
    this.boSungDieuKhoanService.getTrangThaiHieuLuc().subscribe((res) => {
      this.listTrangThaiHieuLuc = res;
    });
  }
  searchDieuKhoan() {
    this.uncheckAll();
    this.isReload = true;
    this.filterBody = { sort: "score", order: "desc" };
    this.selectedSort = "Sắp xếp";
    this.typeSort = "arrow-down-circle";
    this.currentLabelFilter = "Tất cả"; // Reset filter to "Tất cả" when performing new search
    this.paramSearch = {
      filter: this.filterString,
      query: this.searchString,
      domain_list: this.listId,
      co_quan_ban_hanh: this.valueCoQuanBanHanh,
      don_vi: this.valueCoQuanQuanLy,
      loai_van_ban: this.valueLoaiVanBan,
      tinh_trang_hieu_luc: this.valueTrangThai,
      page: this.page,
      search_legal_term: this.search_legal_term,
      ngay_ban_hanh: this.valueNgayBanHanh,
      sort: "loai_van_ban",
      order: "desc",
    };

    this.boSungDieuKhoanService
      .searchDieuKhoan(
        this.limit,
        this.filterString,
        this.searchString,
        this.listId,
        this.valueCoQuanBanHanh,
        this.valueCoQuanQuanLy,
        this.valueLoaiVanBan,
        this.valueTrangThai,
        this.page,
        this.search_legal_term,
        this.valueNgayBanHanh,
        this.must,
        this.should,
        this.not,
        "loai_van_ban",
        "desc"
      )
      .subscribe(
        (res) => {
          this.isReload = false;
          this.currentPage = 1;
          if (res.total == 0) {
            this.noData = true;
          } else {
            this.noData = false;
          }
          // this.cacheDataService.setcacheData(res); // lưu cache data vào service
          this.dataExport = res;
          this.dataTimKiemThongThuong = res.data;
          this.dataTimKiemThongThuongFilter = res.data;
          const allStatistic = {
            label: "Tất cả",
            count: this.dataTimKiemThongThuong?.length || 0,
          };
          this.statistic = res.statistic.filter((item) => item.count > 0);
          if (allStatistic.count > 0) {
            this.statistic.unshift(allStatistic);
          }
          // Ensure filter is reset to "Tất cả" after new search results
          this.currentLabelFilter = "Tất cả";

          this.toast.info(res.msg, "Tìm kiếm", {
            closeButton: true,
            positionClass: "toast-top-right",
            toastClass: "toast ngx-toastr",
          });
          this.totalItem = res.total;
        },
        (error) => {
          this.isReload = false;
        }
      );
  }
  changeTypeSearch(e) {
    this.hideSearchAdvanced = false;
    if (e.target.checked) {
      this.isSearchAdvance = true;
    } else {
      this.isSearchAdvance = false;
      // Reset toàn bộ điều kiện giống logic phần Thêm điều kiện bên Tìm kiếm thông minh
      this.filterString = "";
      this.listId = [];
      this.valueCoQuanBanHanh = [];
      this.valueCoQuanQuanLy = [];
      this.valueLoaiVanBan = [];
      this.valueTrangThai = [];
      this.valueNgayBanHanh = null;
      this.must = "";
      this.should = "";
      this.not = "";
      this.sat_nhap_tinh = false;
      this.boMoi = false;
      this.updateLists();
    }
  }

  toggleTypeSearch() {
    this.search_legal_term = !this.search_legal_term;
    this.dataTimKiemThongThuongFilter = [];
  }

  changeTypeView(e) {
    if (e.target.checked) {
      this.isShowTable = true;
    } else {
      this.isShowTable = false;
    }
  }
  onActivate(e) {
    if (e.type == "click" && e.column.name) {
      window.open(e.row.url);
    }
  }
  transform(value: string): string {
    return value.replace(/<[^>]+>/g, ""); // Xóa tất cả thẻ HTML
  }

  export() {
    this.boSungDieuKhoanService.exportFile(this.dataExport).subscribe((res) => {
      const link = document.createElement("a");
      link.href = window.URL.createObjectURL(res);

      // Lấy từ khóa người dùng nhập
      const rawName = (this.searchString || "").trim();

      // Làm sạch ký tự không hợp lệ trong tên file
      const safeName = rawName
        ? rawName.replace(/[\\/:*?"<>|]/g, "_")
        : "Ket_qua_bo_sung_van_ban";

      // Đặt tên file theo từ khóa
      link.download = `${safeName}.xlsx`;

      link.click();
    });
  }

  selectTypeDoiChieu(e) {
    this.listId = e.map((item) => {
      return item.value;
    });
  }
  selectCoQuanBanHanh(e) {
    this.valueCoQuanBanHanh = e.map((item) => {
      return item.value;
    });
  }
  selectCoQuanQuanLy(e) {
    this.valueCoQuanQuanLy = e.map((item) => {
      return item.value;
    });
  }
  selectLoaiVanBan(e) {
    this.valueLoaiVanBan = e.map((item) => {
      return item.value;
    });
  }
  selectTrangThaiHieuLuc(e) {
    this.valueTrangThai = e.map((item) => {
      return item.value;
    });
  }
  changeNgayBanHanh(e) {
    // console.log(e.target.value);

    this.valueNgayBanHanh = e.target.value;
  }
  setPage(e) {
    this.page = e.offset + 1;
    this.searchDieuKhoan();
  }
  onPageChange(e) {
    this.page = e;
    this.searchDieuKhoan();
  }
  get paginatedData() {
    return this.paginationService.paginate(
      this.dataTimKiemThongThuongFilter,
      this.currentPage,
      this.itemsPerPage
    );
  }
  get totalPageThongThuong() {
    return this.paginationService.getTotalPages(
      this.dataTimKiemThongThuongFilter,
      this.itemsPerPage
    );
  }

  selectSort(sort: string) {
    this.selectedSort =
      sort === "loai_van_ban"
        ? "Hiệu lực pháp lý"
        : sort === "ngay_co_hieu_luc"
        ? "Ngày có hiệu lực"
        : sort === "score"
        ? "Tương đồng nội dung"
        : "Tình trạng hiệu lực";

    this.filterBody.sort = sort;
    this.boSungDieuKhoanService
      .searchDieuKhoan(
        this.limit,
        this.filterString,
        this.searchString,
        this.listId,
        this.valueCoQuanBanHanh,
        this.valueCoQuanQuanLy,
        this.valueLoaiVanBan,
        this.valueTrangThai,
        this.page,
        this.search_legal_term,
        this.valueNgayBanHanh,
        this.must,
        this.should,
        this.not,
        this.filterBody.sort,
        this.filterBody.order
      )
      .subscribe((res) => {
        this.currentPage = 1;
        this.table ? (this.table.offset = 0) : null;

        this.dataExport = res;
        this.dataTimKiemThongThuong = res.data;

        this.toast.info(res.msg, "Tìm kiếm", {
          closeButton: true,
          positionClass: "toast-top-right",
          toastClass: "toast ngx-toastr",
        });
        this.totalItem = res.total;
        this.filterResult("Tất cả");
      });
  }
  loaiSapXep() {
    if (this.typeSort == "arrow-down-circle") {
      this.typeSort = "arrow-up-circle";
      this.filterBody.order = "asc";
      this.boSungDieuKhoanService
        .searchDieuKhoan(
          this.limit,
          this.filterString,
          this.searchString,
          this.listId,
          this.valueCoQuanBanHanh,
          this.valueCoQuanQuanLy,
          this.valueLoaiVanBan,
          this.valueTrangThai,
          this.page,
          this.search_legal_term,
          this.valueNgayBanHanh,
          this.must,
          this.should,
          this.not,
          this.filterBody.sort,
          this.filterBody.order
        )
        .subscribe((res) => {
          this.currentPage = 1;
          this.table ? (this.table.offset = 0) : null;

          this.dataExport = res;
          this.dataTimKiemThongThuong = res.data;

          this.toast.info(res.msg, "Tìm kiếm", {
            closeButton: true,
            positionClass: "toast-top-right",
            toastClass: "toast ngx-toastr",
          });
          this.totalItem = res.total;
          this.filterResult("Tất cả");
        });
    } else {
      this.typeSort = "arrow-down-circle";
      this.filterBody.order = "desc";
      this.boSungDieuKhoanService
        .searchDieuKhoan(
          this.limit,
          this.filterString,
          this.searchString,
          this.listId,
          this.valueCoQuanBanHanh,
          this.valueCoQuanQuanLy,
          this.valueLoaiVanBan,
          this.valueTrangThai,
          this.page,
          this.search_legal_term,
          this.valueNgayBanHanh,
          this.must,
          this.should,
          this.not,
          this.filterBody.sort,
          this.filterBody.order
        )
        .subscribe((res) => {
          this.currentPage = 1;
          this.table ? (this.table.offset = 0) : null;

          this.dataExport = res;
          this.dataTimKiemThongThuong = res.data;

          this.toast.info(res.msg, "Tìm kiếm", {
            closeButton: true,
            positionClass: "toast-top-right",
            toastClass: "toast ngx-toastr",
          });
          this.totalItem = res.total;
        });
    }
  }
  saveHistoryFiles() {
    if (this.search_legal_term) {
      // Thêm điều khoản liên quan
      const formData = new FormData();
      const listIdClause = this.selectedFiles
        .map((file) => {
          return file.id;
        })
        .join(",");
      formData.append("clause_id", listIdClause);
      this.boSungDieuKhoanService
        .addClause(this.idClauseParent, formData)
        .subscribe((res) => {
          this.uncheckAll();
          const toastFn =
            res.status === "warning" ? this.toast.warning : this.toast.success;
          toastFn.call(this.toast, res.message, res.status_title, {
            closeButton: true,
            positionClass: "toast-top-right",
            toastClass: "toast ngx-toastr",
            enableHtml: true,
          });

          this.detailClauseService.isSaveResoultFromChatBot.next(true);
        });
    } else {
      // Thêm văn bản
      const listIdFile = this.selectedFiles.map((file) => {
        return file.id;
      });

      const body = {
        // document_id: this.fileId,

        document_ids: listIdFile,
        relation_type:
          this.typeAddFile == "Văn bản căn cứ"
            ? "van_ban_can_cu"
            : "van_ban_lien_quan_khac",
      };
      this.boSungDieuKhoanService
        .addRelateDocument(body, this.fileId)
        .subscribe((res) => {
          this.uncheckAll();
          const toastFn =
            res.status === "warning" ? this.toast.warning : this.toast.success;
          toastFn.call(this.toast, res.message, res.status_title, {
            closeButton: true,
            positionClass: "toast-top-right",
            toastClass: "toast ngx-toastr",
            enableHtml: true,
          });
          this.viewDetailFileService.isSaveFileFromSearch.next(true);
        });
    }
  }
  onSearch(event: KeyboardEvent) {
    if (event.key === "Enter" && this.searchQuery.trim() !== "") {
      this.boSungDieuKhoanService.searchHistory(this.searchQuery).subscribe(
        (data) => {
          this.searchResults = data;
        },
        (error) => {
          console.error("Lỗi khi tìm kiếm:", error);
        }
      );
    }
  }
  filterResult(label) {
    this.currentPage = 1;
    if (this.table) this.table.offset = 0;
    this.selectedFiles = []; // Xóa danh sách các mục đã chọn
    this.currentLabelFilter = label;
    // Lọc dữ liệu theo label (use item.label to match statistic categories)
    const filteredData = this.dataTimKiemThongThuong.filter(
      (item) => item.label === label
    );

    // Nếu không có dữ liệu hoặc label là null/undefined/"Tất cả", hiển thị tất cả
    this.dataTimKiemThongThuongFilter =
      filteredData.length > 0 ? filteredData : this.dataTimKiemThongThuong;
  }
  onCheckboxChange(item: any, event: Event) {
    const isChecked = (event.target as HTMLInputElement).checked;

    if (isChecked) {
      this.selectedFiles.push(item); // Thêm item vào danh sách chọn
    } else {
      this.selectedFiles = this.selectedFiles.filter((i) => i !== item); // Bỏ item khỏi danh sách chọn
    }
    // // console.log("Các mục được chọn:", this.selectedFiles);
  }
  uncheckAll() {
    this.selectedFiles = []; // Xóa danh sách các mục đã chọn
    const checkboxes = document.querySelectorAll(
      ".checkboxSelectedFile"
    ) as NodeListOf<HTMLInputElement>;
    checkboxes.forEach((checkbox) => (checkbox.checked = false)); // Bỏ chọn tất cả checkbox
  }
  changeLimitSize(event) {
    this.limit = event;
  }
  toggleNewDistrict(event?: any) {
    this.sat_nhap_tinh = event ? event.target.checked : !this.sat_nhap_tinh;
    this.updateLists();
  }

  toggleBoMoi(event?: any) {
    this.boMoi = event ? event.target.checked : !this.boMoi;
    this.updateLists();
  }

  // Hàm cập nhật danh sách cơ quan theo tổ hợp 2 checkbox
  private updateLists() {
    // reset các giá trị đã chọn
    this.valueCoQuanBanHanh = [];
    this.valueCoQuanQuanLy = [];

    if (this.sat_nhap_tinh && this.boMoi) {
      // TinhMoi + BoMoi
      this.listCoQuanBanHanh = CoQuanBanHanhTinhMoiBoMoi;
      this.listCoQuanQuanLy = CoQuanQuanLyTinhMoiBoMoi;
    } else if (this.sat_nhap_tinh && !this.boMoi) {
      // TinhMoi + BoCu
      this.listCoQuanBanHanh = CoQuanBanHanhTinhMoiBoCu;
      this.listCoQuanQuanLy = CoQuanQuanLyTinhMoiBoCu;
    } else if (!this.sat_nhap_tinh && this.boMoi) {
      // TinhCu + BoMoi
      this.listCoQuanBanHanh = CoQuanBanHanhTinhCuBoMoi;
      this.listCoQuanQuanLy = CoQuanQuanLyTinhCuBoMoi;
    } else {
      // TinhCu + BoCu
      this.listCoQuanBanHanh = CoQuanBanHanhTinhCuBoCu;
      this.listCoQuanQuanLy = CoQuanQuanLyTinhCuBoCu;
    }
  }
  closeBoSung() {
    // Đóng modal nếu đang mở trong modal, nếu không thì ẩn sidebar
    this.modalService.dismissAll();
    this.listDocumentService.rightSideBarValue.next(ShowSideBar.Note);
  }
  showTermClause(index) {
    this.isShowTerm = !this.isShowTerm;
    this.indexTerm = index;
    if (!this.isShowTerm) {
      this.indexTerm = -1;
    }
  }
  ngOnDestroy() {
    this.workspace.isSaveFileFromSearch.next(false);
    this.detailClauseService.isSaveResoultFromChatBot.next(false);
    this.viewDetailFileService.isSaveFileFromSearch.next(false);
    this.unSubAll.next(null);
    this.unSubAll.complete();
  }
}
