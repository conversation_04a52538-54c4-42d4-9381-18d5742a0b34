<div
  [ngClass]="{
    'custom-dialog': true,
    'maximized d-flex': isMaximized,
    'chatbot-container-relative': !isMaximized
  }"
  class="container-chatbot"
>
  <div
    class="header-chatbot d-flex justify-content-between"
    *ngIf="!isMaximized"
  >
    <div class="button-add-conversation-minimized" (click)="addNewChat()">
      <div
        class="button-add-chatbot"
        ngbTooltip="Tạo cuộc hội thoại mới"
        container="body"
      >
        <img src="assets/images/icons/new-chatbot.svg" alt="plus" />
      </div>
    </div>
  </div>

  <!-- Mobile Menu Button - Hiển thị khi width <= 768px -->
  <button
    *ngIf="isMaximized && !isMobileSidebarOpen"
    class="mobile-menu-toggle"
    (click)="toggleMobileSidebar()"
    [ngClass]="{ 'menu-open': isMobileSidebarOpen }"
  >
    <i data-feather="menu"></i>
  </button>

  <!-- minimize/close button -->
  <!-- MAX + chưa mở canvas: nút THU NHỎ ở góc trên-phải -->
  <img
    *ngIf="isMaximized && type != 5 && !splitView"
    class="chatbot-top-right-button cursor-pointer button-add-conversation z-9999"
    (click)="minimumChatbot()"
    src="assets/images/icons/minimum.svg"
    alt="Thu nhỏ"
    ngbTooltip="Thu nhỏ"
    container="body"
  />

  <!-- MAX + đang mở canvas: chỉ hiển thị nút X ở góc trên-phải -->
  <img
    *ngIf="isMaximized && type != 5 && splitView"
    class="chatbot-top-right-button cursor-pointer button-add-conversation z-9999"
    (click)="closeCanvas()"
    src="assets/images/icons/x.svg"
    alt="Đóng vùng soạn thảo"
    ngbTooltip="Đóng vùng soạn thảo"
    container="body"
  />

  <!-- Popup riêng (type == 5) vẫn như cũ -->
  <img
    *ngIf="type == 5"
    class="chatbot-top-right-button cursor-pointer button-add-conversation z-9999"
    (click)="modal.dismiss('Cross click')"
    src="assets/images/icons/x.svg"
    alt="x"
    ngbTooltip="Đóng"
    container="body"
  />
  <!-- minimize/close button-->

  <!-- Overlay cho mobile -->
  <div
    class="mobile-sidebar-overlay"
    *ngIf="isMobileSidebarOpen"
    (click)="toggleMobileSidebar()"
  ></div>

  <div
    class="chatbot-sidebar col-3 col-xl-2 p-0"
    [ngClass]="{
      'd-flex flex-column': isMaximized,
      'd-none': !isMaximized,
      'mobile-sidebar-open': isMobileSidebarOpen
    }"
  >
    <!-- Close button cho mobile sidebar -->
    <button class="mobile-sidebar-close" (click)="toggleMobileSidebar()">
      <i class="width-20px height-20px" data-feather="x"></i>
    </button>

    <div
      class="new-chat d-flex justify-content-start justify-content-lg-between p-50"
    >
      <span class="d-flex align-items-center">
        <p class="ml-50 mb-0 h4">
          {{ workSpaceName ? workSpaceName : "Danh sách đoạn chat" }}
        </p>
      </span>
      <div
        class="button-add-conversation"
        ngbTooltip="Tạo cuộc hội thoại mới"
        container="body"
        (click)="addNewChat(); toggleMobileSidebar()"
      >
        <div class="button-add-chatbot">
          <img src="assets/images/icons/new-chatbot.svg" alt="new-chatbot" />
        </div>
      </div>
    </div>
    <div class="p-50">
      <div class="form-group">
        <input
          type="text"
          class="form-control m-0"
          id="basicInput"
          placeholder="Tìm kiếm đoạn chat"
          [(ngModel)]="searchConversationString"
          (ngModelChange)="filterConversations()"
        />
      </div>
    </div>
    <div class="coversation-contain">
      <ng-container *ngFor="let group of groupedConversationsFilter">
        <div class="chatbot-group-label my-1" *ngIf="group.items.length > 0">
          {{ group.label }}
        </div>

        <ng-container *ngFor="let item of group.items">
          <p
            (click)="onConversationChange(item); toggleMobileSidebar()"
            (contextmenu)="
              onRightClickChatbotMessage($event, item); $event.stopPropagation()
            "
            class="m-0 p-50 coversation w-100"
            [ngClass]="{
              selected: selectedConversation?.id == item.id,
              truncate: !(isEditChatbotName && selectedForRename?.id == item.id)
            }"
            [contentEditable]="
              isEditChatbotName && selectedForRename?.id == item.id
            "
            (keydown.enter)="onEditNameEnter($event, item)"
            (blur)="onEditNameBlur($event, item)"
            [ngbTooltip]="item.name"
            placement="right"
            container="body"
          >
            {{ item.name }}
          </p>
        </ng-container>
      </ng-container>
      <!-- content menu -->
      <div
        class="context-menu px-50"
        *ngIf="contextMenuVisible"
        [style.top.px]="contextMenuPosition.y"
        [style.left.px]="contextMenuPosition.x"
        (click)="closeContextMenu()"
      >
        <ul>
          <li (click)="renameChatbot(contextMenuItem)">
            <img
              class="mr-50"
              src="assets/images/icons/pencil.svg"
              alt="pencil"
            />
            Đổi tên
          </li>
          <li (click)="deleteChatbot(contextMenuItem)">
            <img
              class="mr-50"
              src="assets/images/icons/trash.svg"
              alt="trash"
            />
            Xoá
          </li>
        </ul>
      </div>
      <!-- content menu -->
    </div>
  </div>

  <div
    class="chatbot-main-flex-size chat-main-flex d-flex flex-column flex-grow-1"
    [ngClass]="{ 'w-100': !isMaximized }"
  >
    <div
      *ngIf="messages?.length != 0"
      [ngClass]="{ 'chat-content-custom': !isMaximized }"
      class="chat-content"
      #chatContainer
      (scroll)="onChatScroll()"
      (mouseup)="onTextSelect()"
    >
      <div
        class="mt-1 p-0"
        [ngClass]="
          isMaximized ? 'col-10 chat-content-maximized mx-auto p-1 ' : ''
        "
      >
        <div
          *ngFor="let message of messages; let last = last; let i = index"
          class="message-bubble p-0"
        >
          <span
            class="collapse-icon"
            *ngIf="message.thinking && message.thinking != '<think>'"
            id="chatbot"
            [ngClass]="{
              user: message.role == 'user',
              bot: message.role == 'assistant'
            }"
          >
            <ngb-accordion #acc="ngbAccordion" activeIds="ngb-panel-1">
              <ngb-panel id="ngb-panel-1" [open]="true">
                <ng-template ngbPanelTitle>
                  <span class="lead collapse-title card-title"
                    ><strong class="font-sm"> Tiến trình tư duy </strong>
                  </span>
                </ng-template>
                <ng-template ngbPanelContent>
                  <p
                    class="thinking mt-1 wrap-text"
                    [innerHTML]="message.thinking"
                  ></p>
                </ng-template>
              </ngb-panel>
            </ngb-accordion>
          </span>
          <div
            class="answer mt-1 user wrap-text"
            *ngIf="message.role == 'user'"
          >
            <!-- selection text -->
            <ng-container *ngIf="message.selection_text">
              <div class="bubble-header">
                <img
                  class="icon"
                  src="assets/images/icons/selection-text.svg"
                  alt="selection_text"
                />
                <p class="selection-text" [ngbTooltip]="message.selection_text">
                  {{ message.selection_text }}
                </p>
              </div>
            </ng-container>
            <!-- selection text -->

            <!-- message user -->
            <span
              class="d-flex ml-25"
              [ngClass]="{
                'ml-25':
                  message.selected_files?.upload_files?.length ||
                  message.selected_files?.save_files?.length
              }"
              [innerHTML]="message.answer"
            ></span>
            <!-- message user -->

            <!-- selection file -->
            <ng-container
              *ngIf="
                message.selected_files?.upload_files?.length ||
                message.selected_files?.save_files?.length
              "
            >
              <!-- Hiển thị upload_files -->
              <div class="file-list-wrapper">
                <button
                  class="collapse-btn p-0"
                  (click)="collapsed = !collapsed"
                >
                  Tài liệu đính kèm<img
                    [src]="
                      collapsed
                        ? 'assets/images/icons/up.svg'
                        : 'assets/images/icons/down-black.svg'
                    "
                    alt="collapsed"
                  />
                </button>

                <div class="file-list" [class.collapsed]="collapsed">
                  <ng-container
                    *ngFor="let file of message.selected_files.upload_files"
                  >
                    <div
                      class="file-item mb-50"
                      [ngbTooltip]="file.name"
                      placement="left"
                      container="body"
                      (click)="onOpenDocument(file)"
                      [ngClass]="{ 'active': isActiveFile(file) }"
                    >
                      {{ file.name }}
                    </div>
                  </ng-container>

                  <ng-container
                    *ngFor="let file of message.selected_files.save_files"
                  >
                    <div
                      class="file-item mb-50"
                      [ngbTooltip]="file.name"
                      placement="left"
                      container="body"
                      (click)="onOpenDocument(file)"
                      [ngClass]="{ 'active': isActiveFile(file) }"
                    >
                      {{ file.name }}
                    </div>
                  </ng-container>
                </div>
              </div>

              <!-- Hiển thị upload_files -->
            </ng-container>
            <!-- selection file -->
          </div>
          <p class="gradient-text fly-text" *ngIf="last && !checkDoneThinking">
            {{ statusThinking }}
          </p>

          <div
            #replyMessage
            *ngIf="message.role === 'assistant'"
            class="answer mt-1 bot"
            [attr.id]="'msg-' + i"
            #answerContainer
            (click)="onAnswerClick($event)"
          >
            <!-- 1. Phần text phía trên block ``` (hoặc toàn bộ answer nếu không soạn thảo) -->
            <div
              *ngIf="message.answerTop"
              [innerHTML]="message.answerTop | markdown | safe"
            ></div>

            <!-- 2. Luồng cũ: KHÔNG có soạn thảo => render toàn bộ answer -->
            <div
              *ngIf="!message.answerTop && !message.fileAttached"
              [innerHTML]="message.answer | markdown | safe"
            ></div>

            <!-- 3. Card file (DRAFT / văn bản) -->
            <div *ngIf="message.fileAttached" class="mt-50">
              <div
                class="chatbot-file-card"
                (click)="viewFileAttached(message.fileAttached)"
                [ngClass]="{ 'active': isActiveFile(message.fileAttached) }"
              >
                <div class="chatbot-file-card__icon">
                  <img src="assets/images/icons/file-search.svg" alt="file" />
                </div>

                <div class="chatbot-file-card__body">
                  <div class="chatbot-file-card__title one-line">
                    {{
                      message.fileAttached?.label
                        || message.fileAttached?.so_hieu
                        || message.fileAttached?.trich_yeu
                        || message.fileAttached?.name
                    }}
                  </div>

                  <div class="chatbot-file-card__meta">
                    <span class="chatbot-file-card__meta-text">
                      {{
                        (message.fileAttached?.created_at
                          || message.fileAttached?.updated_at
                          || message.fileAttached?.createdAt
                        ) | date: "HH:mm dd/MM/yyyy"
                      }}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            <!-- 4. Phần text phía dưới block ``` (ghi chú thêm của chatbot) -->
            <div
              *ngIf="message.answerBottom"
              class="answer-text-bottom mt-50"
              [innerHTML]="message.answerBottom | markdown | safe"
            ></div>
          </div>

          <ng-container *ngIf="message.role == 'assistant' && checkDoneAnswer">
            <span
              class="cursor-pointer"
              ngbTooltip="Chỉnh sửa"
              container="body"
              (click)="editQuestion(i)"
              ><img
                src="assets/images/icons/pencil.svg"
                class="chatbot-icon-padding-5 icon-button"
                alt="pen"
            /></span>
            <span
              class="cursor-pointer p-50 icon-button"
              appcopy
              [copyTarget]="'#msg-' + i"
              ><img
                src="assets/images/icons/copy.svg"
                ngbTooltip="Sao chép"
                container="body"
                alt="copy"
            /></span>
            <!-- Nút phát khi chưa đọc -->
            <span
              *ngIf="!isSpeechToText"
              class="cursor-pointer"
              ngbTooltip="Đọc to"
              container="body"
              (click)="textToSpeech(message)"
            >
              <img
                src="assets/images/icons/volume.svg"
                class="icon-button"
                alt="volume"
              />
            </span>

            <!-- Nút tạm dừng khi đang phát -->
            <span
              *ngIf="
                isSpeechToText &&
                !isPaused &&
                idMessageSpeechToText == message.id
              "
              class="cursor-pointer"
              ngbTooltip="Tạm dừng"
              container="body"
              (click)="pauseTextToSpeech()"
            >
              <img
                src="assets/images/icons/pause.svg"
                class="icon-button"
                alt="pause"
              />
            </span>

            <!-- Nút tiếp tục khi đang tạm dừng -->
            <span
              *ngIf="
                isSpeechToText &&
                isPaused &&
                idMessageSpeechToText == message.id
              "
              class="cursor-pointer"
              ngbTooltip="Tiếp tục"
              container="body"
              (click)="resumeTextToSpeech()"
            >
              <img
                src="assets/images/icons/play.svg"
                class="icon-button"
                alt="continue"
              />
            </span>

            <!-- Nút dừng hẳn -->
            <span
              *ngIf="isSpeechToText && idMessageSpeechToText == message.id"
              class="cursor-pointer"
              ngbTooltip="Dừng đọc"
              container="body"
              (click)="endTextToSpeech()"
            >
              <img
                src="assets/images/icons/stop-spt.svg"
                class="icon-button"
                alt="stop"
              />
            </span>

            <span
              class="cursor-pointer"
              ngbTooltip="Câu trả lời tốt"
              container="body"
              (click)="
                feedbackChatbot(
                  message,
                  message.feedback == 'like' ? 'unlike' : 'like'
                )
              "
              ><img
                [src]="
                  message.feedback == 'like'
                    ? 'assets/images/icons/liked.svg'
                    : 'assets/images/icons/like.svg'
                "
                alt="like"
                class="icon-button"
            /></span>
            <span
              class="cursor-pointer"
              ngbTooltip="Câu trả lời không tốt"
              container="body"
              (click)="
                feedbackChatbot(
                  message,
                  message.feedback == 'dislike' ? 'undislike' : 'dislike'
                )
              "
              ><img
                [src]="
                  message.feedback == 'dislike'
                    ? 'assets/images/icons/disliked.svg'
                    : 'assets/images/icons/dislike.svg'
                "
                alt="dislike"
                class="icon-button"
            /></span>
          </ng-container>
        </div>
        <div [style.height.px]="chatHeight" *ngIf="!checkDoneAnswer"></div>
      </div>
      <div
        class="selection-popover"
        *ngIf="showPopover"
        #selectionPopover
        [ngStyle]="{
          top: popoverPosition.top + 'px',
          left: popoverPosition.left + 'px'
        }"
        (click)="$event.stopPropagation()"
      >
        <button class="btn-note" (click)="createNoteFromSelection()">
          <i data-feather="file" size="19"></i>Tạo ghi chú nhanh
        </button>
      </div>
    </div>
    <span
      id="scrollDownBtn"
      class="scroll-to-bottom"
      *ngIf="showScrollButton && messages?.length > 0"
      (click)="scrollToBottom2()"
      ngbTooltip="Lướt đến mới nhất"
      container="body"
      ><i data-feather="arrow-down" size="24"></i
    ></span>
    <div
      class="chat-input-container"
      [ngClass]="{
        'centered-input': messages?.length == 0,
        'w-100': isMaximized
      }"
    >
      <div class="w-100 text-center mb-1">
        <b class="chatbot-empty-title" *ngIf="messages?.length == 0"
          >Tôi có thể giúp gì cho bạn ?</b
        >
      </div>
      <span class="chatbot-quota-banner text-primary-theme font-weight-bolder">
        Còn {{ quota }} lượt truy vấn
      </span>
      <div class="input-container">
        <div class="function-chat d-flex justify-content-start mb-50">
          <div
            class="functions-button"
            *ngIf="selectedFile.length > 0"
            [ngbPopover]="addFilePopover"
            placement="top"
            [autoClose]="'outside'"
            container="body"
          >
            <img src="assets/images/icons/file-search.svg" alt="file" />

            <span (click)="clearDocument($event)">
              {{ selectedFile?.length }} Tài liệu<img
                src="assets/images/icons/x.svg"
                alt="x"
              />
            </span>
          </div>
          <div
            class="functions-button"
            *ngIf="isHasTextFromVanBan"
            [ngbTooltip]="selection_text"
            container="body"
          >
            <img src="assets/images/icons/text-scan.svg" alt="file" /> Đã chọn
            ({{ selection_text?.length }})
            <span (click)="selection_text = null; isHasTextFromVanBan = false">
              <img src="assets/images/icons/x.svg" alt="x" />
            </span>
          </div>
        </div>
        <div
          [ngbTooltip]="selection_text"
          container="body"
          class="function-chat d-flex justify-content-start"
          *ngIf="selection_text && !isHasTextFromVanBan"
        >
          <div class="icon-line">
            <div class="icons">
              <img
                src="assets/images/icons/arrow-right-down.svg"
                alt="arrow-right-down"
              />
            </div>
            <div class="content-text">
              {{ selection_text }}
            </div>
            <div class="icons" (click)="selection_text = null">
              <img src="assets/images/icons/x.svg" alt="x" />
            </div>
          </div>
        </div>

        <textarea
          spellcheck="false"
          id="queryChatbot"
          #queryInput
          [(ngModel)]="userInput"
          (keydown)="sendMessage($event)"
          (input)="autoResize($event)"
          class="chat-input w-100"
          placeholder="Nhập truy vấn của bạn vào đây"
          rows="1"
        ></textarea>
        <div
          class="tool-chatbot d-flex justify-content-between align-items-center"
        >
          <div class="tool d-flex align-items-center flex-wrap">
            <div
              *ngIf="type != 5"
              class="cursor-pointer mr-50 icon-button"
              [ngbTooltip]="
                listDocument.length > 0
                  ? 'Thêm tài liệu'
                  : 'Tải lên hoặc lưu văn bản vào danh sách văn bản để thêm vào đây'
              "
              container="body"
              [ngbPopover]="listDocument.length > 0 ? addFilePopover : null"
              placement="top"
              [autoClose]="'outside'"
            >
              <img src="assets/images/icons/plus-2.svg" alt="file" />
            </div>
            <div
              class="cursor-pointer mr-50 icon-button"
              ngbTooltip="Công cụ"
              container="body"
              [ngbPopover]="showToolsPopover"
              #toolsPop="ngbPopover" 
              [autoClose]="'outside'"
            >
              <img src="assets/images/icons/adjust.svg" alt="file" />
              <span class="tool-label"> Công cụ </span>
            </div>
            <div
              *ngIf="toolSelected?.length > 0"
              class="d-flex cursor-pointer flex-wrap badge-container"
            >
              <ng-container *ngFor="let item of toolSelected; let i = index">
                <p
                  class="m-0 d-flex align-items-center badge badge-pill badge-light-primary-theme"
                >
                  <img [src]="item.icon" alt="icon" /> &nbsp; {{ item.label }}
                  <img
                    (click)="removeTool(i)"
                    [src]="'assets/images/icons/x-danger.svg'"
                    alt="x"
                  />
                </p>
              </ng-container>
            </div>
          </div>
          <div class="chat-button-wrapper">
            <button
              class="chat-send-button"
              (click)="clickSendMessage()"
              *ngIf="checkDoneAnswer"
              [ngClass]="{ 'disabled-button': userInput === '' }"
            >
              <img src="assets/images/icons/send.svg" alt="send " />
            </button>
            <button
              class="chat-cancel-button"
              (click)="cancelRequest()"
              *ngIf="!checkDoneAnswer"
            >
              <img src="assets/images/icons/stop.svg" alt="send " />
            </button>
          </div>
        </div>
      </div>

      <div class="w-100 text-center">
        <small class="chatbot-inline-block text-muted my-xxl-50 my-xl-1 m-lg-1">
          Câu trả lời có thể gặp lỗi, vui lòng kiểm tra lại thông tin.
        </small>
      </div>
    </div>
  </div>
  <!-- === RESIZER === -->
  <div
    *ngIf="isMaximized && splitView"
    class="pane-resizer"
    (mousedown)="startResize($event)"
  >
    <!-- Nút THU NHỎ bám theo thanh kéo -->
    <img
      *ngIf="type != 5"
      class="chatbot-minimize-on-resizer cursor-pointer"
      (click)="minimumChatbot(); $event.stopPropagation()"
      src="assets/images/icons/minimum.svg"
      alt="Thu nhỏ"
      ngbTooltip="Thu nhỏ"
      container="body"
    />
  </div>


  <!-- === CANVAS PANE === -->
  <aside
    *ngIf="isMaximized && splitView"
    class="canvas-pane"
    [style.width.%]="rightWidthPct">

    <div class="canvas-body">
      <app-view-detail-file class="h-100 w-100"></app-view-detail-file>
    </div>
  </aside>
</div>
<ng-template *ngIf="!isMaximized" #viewFileModal let-modal>
  <app-view-detail-file [modal]="modal" [type]="types"></app-view-detail-file>
</ng-template>
<ng-template #addFilePopover let-modal>
  <div class="container-document-chatbot">
    <div class="p-50 align-items-center document-chatbot">
      <div class="custom-control custom-checkbox">
        <input
          (click)="toggleAll($event)"
          type="checkbox"
          class="custom-control-input"
          [id]="'customCheck2' + i"
          [checked]="isAllSelected()"
        />
        <label class="custom-control-label" [for]="'customCheck2' + i">
          <p class="m-0">
            {{
              listDocument.length > 0
                ? "Chọn tất cả"
                : "Tải lên hoặc lưu văn bản vào danh sách văn bản để thêm vào đây"
            }}
          </p>
        </label>
      </div>
    </div>
    <ng-container *ngFor="let item of listDocument; let i = index">
      <div class="p-50 align-items-center document-chatbot">
        <div class="custom-control custom-checkbox">
          <input
            (click)="toggleSelection(i)"
            type="checkbox"
            class="custom-control-input"
            [id]="'customCheck1' + i"
            [checked]="selectedDocuments[i]"
          />
          <label class="custom-control-label" [for]="'customCheck1' + i">
            <div class="one-line d-flex align-items-center">
              <img [src]="getOfficeIcon(item)" alt="PDF" class="icon" />
              <p class="one-line m-0" [ngbTooltip]="item.name">
                {{ i + 1 }}.{{ item.name }}
              </p>
            </div>
          </label>
        </div>
      </div>
    </ng-container>
  </div>
</ng-template>
<ng-template #showToolsPopover let-modal>
  <div class="text-dark">
    <ng-container *ngFor="let item of listTool; let i = index">
      <div
        *ngIf="shouldShowTool(item)"
        class="p-1 tools"
        [class.has-submenu]="item.children?.length"
        [class.disabled]="isToolDisabled(item)"
        (mouseenter)="onToolHover(item)"
        (mouseleave)="onToolLeave(item)"
        (click)="onToolClick($event, item)"
      >
        <div class="tool-item">
          <img [src]="item.icon" alt="icon" />
          <span>{{ item.label }}</span>
        </div>
        <span class="submenu-arrow" *ngIf="item.children?.length">&#8250;</span>

        <div
          class="tools-submenu"
          *ngIf="item.children?.length && activeSubmenu === item.value"
          (mouseenter)="onSubmenuEnter(item)"
          (mouseleave)="onSubmenuLeave()"
          (click)="$event.stopPropagation()"
        >
          <p
            *ngFor="let child of item.children"
            (click)="selectSubTool(child, $event)"
            [class.disabled]="isSubToolSelected(child.value)"
          >
            {{ child.label }}
          </p>
        </div>
      </div>
    </ng-container>
  </div>
</ng-template>
