import {
  Component,
  <PERSON><PERSON><PERSON><PERSON>,
  HostL<PERSON>ener,
  OnDestroy,
  OnInit,
  ViewChild,
  ViewEncapsulation,
  ChangeDetectorRef,
} from "@angular/core";
import { MediaObserver } from "@angular/flex-layout";
import { Router, NavigationEnd } from '@angular/router';
import { TranslateService } from "@ngx-translate/core";
import * as _ from "lodash";
import { Subject } from "rxjs";
import { takeUntil, finalize } from "rxjs/operators";
import { filter } from 'rxjs/operators';
import { CoreSidebarService } from "@core/components/core-sidebar/core-sidebar.service";
import { CoreConfigService } from "@core/services/config.service";
import { CoreMediaService } from "@core/services/media.service";
import { AuthenticationService } from "app/auth/service";

import { User } from "app/auth/models";

import { HttpClient } from "@angular/common/http";
import { FormBuilder, FormGroup, Validators } from "@angular/forms";
import { NgbActiveModal, NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { DetailWorkSpaceService } from "app/main/apps/quan-ly-van-ban/detail-work-space/detail-work-space.service";
import { ToastrService } from "ngx-toastr";
import { getLogoImage as getLogoImageHelper, getAppName as getAppNameHelper, DEFAULT_LOGO_IMAGE } from "app/shared/image.helper";

@Component({
  selector: "app-navbar",
  templateUrl: "./navbar.component.html",
  styleUrls: ["./navbar.component.scss"],
  encapsulation: ViewEncapsulation.None,
})
export class NavbarComponent implements OnInit, OnDestroy {
  @ViewChild("changePassModal") changePassModal: NgbActiveModal;
  @ViewChild("addAccountModal") addAccountModal: NgbActiveModal;
  @ViewChild("editUserModal") editUserModal: NgbActiveModal;
  @ViewChild("reportModal") reportModal: NgbActiveModal;
  public formChangePass: FormGroup;
  public formAddAccount: FormGroup;
  public horizontalMenu: boolean;
  public hiddenMenu: boolean;

  public coreConfig: any;
  public currentSkin: string;
  public prevSkin: string;
  public userName: string;
  public currentUser: User;
  public role: string;
  public languageOptions: any;
  public navigation: any;
  public selectedLanguage: any;
  public showOldPass = false;
  public showNewPass = false;
  public showPassWord = false;
  isCommercialRoute = false;
  @HostBinding("class.fixed-top")
  public isFixed = false;
  public contentReport: string = "";
  @HostBinding("class.navbar-static-style-on-scroll")
  public windowScrolled = false;
  getAppName = getAppNameHelper
  defaultLogo = DEFAULT_LOGO_IMAGE;

  // Add .navbar-static-style-on-scroll on scroll using HostListener & HostBinding
  @HostListener("window:scroll", [])
  onWindowScroll() {
    if (
      (window.pageYOffset ||
        document.documentElement.scrollTop ||
        document.body.scrollTop > 100) &&
      this.coreConfig.layout.navbar.type == "navbar-static-top" &&
      this.coreConfig.layout.type == "horizontal"
    ) {
      this.windowScrolled = true;
    } else if (
      (this.windowScrolled && window.pageYOffset) ||
      document.documentElement.scrollTop ||
      document.body.scrollTop < 10
    ) {
      this.windowScrolled = false;
    }
  }
  getLogoImage(): string {
    return getLogoImageHelper();
  }

  // Private
  private _unsubscribeAll: Subject<any>;
  private isCmsDashboard(url: string): boolean {
    const raw = (url || window.location.pathname || '');
    const path = raw.split('#')[0].split('?')[0].replace(/^\/cls(?=\/)/i, '');
    return /^\/?cms\/dashboard\/?$/i.test(path);
  }

  private isQuanLyVanBanRoot(url: string): boolean {
    const raw = (url || window.location.pathname || '');
    const path = raw.split('#')[0].split('?')[0].replace(/^\/cls(?=\/)/i, '');
    return /^\/?quan-ly-van-ban\/?$/i.test(path);
  }

  private shouldHideBrand(url: string): boolean {
    return this.isCmsDashboard(url) || this.isQuanLyVanBanRoot(url);
  }
  isPasswordChanging: boolean = false;
  isADUser: boolean = false;
  hideCmsLogo = false;   
  constructor(
    private _authenticationService: AuthenticationService,
    private _coreConfigService: CoreConfigService,
    private _coreMediaService: CoreMediaService,
    private _coreSidebarService: CoreSidebarService,
    private _mediaObserver: MediaObserver,
    public _translateService: TranslateService,
    private modalService: NgbModal,
    private fb: FormBuilder,
    private _toast: ToastrService,
    private http: HttpClient,
    private detailWorkspaceService: DetailWorkSpaceService,
    private router: Router,
    private cdr: ChangeDetectorRef,
  ) {
    this._authenticationService.currentUser.subscribe(
      (x) => (this.currentUser = x)
    );

    this.languageOptions = {
      vi: {
        title: "Tiếng Việt",
        flag: "vn",
      },
      en: {
        title: "English",
        flag: "us",
      },
    };

    // Set the private defaults
    this._unsubscribeAll = new Subject();
  }

  // Public Methods
  // -----------------------------------------------------------------------------------------------------

  /**
   * Toggle sidebar open
   *
   * @param key
   */
  toggleSidebar(key): void {
    this._coreSidebarService.getSidebarRegistry(key).toggleOpen();
  }
  get fc() {
    return this.formChangePass.controls;
  }
  getFormControl(name: string) {
    return this.formAddAccount.get(name);
  }
  /**
   * Set the language
   *
   * @param language
   */
  reloadOnce: boolean = true;
  setLanguage(language): void {
    // Set the selected language for the navbar on change
    this.selectedLanguage = language;

    // Use the selected language id for translations
    this._translateService.use(language);

    this._coreConfigService.setConfig(
      { app: { appLanguage: language } },
      { emitEvent: true }
    );
    if (this.reloadOnce) {
      window.location.reload();
      this.reloadOnce = false;
    }
  }

  /**
   * Toggle Dark Skin
   */
  toggleDarkSkin() {
    // Get the current skin
    this._coreConfigService
      .getConfig()
      .pipe(takeUntil(this._unsubscribeAll))
      .subscribe((config) => {
        this.currentSkin = config.layout.skin;
      });

    // Toggle Dark skin with prevSkin skin
    this.prevSkin = localStorage.getItem("prevSkin");

    if (this.currentSkin === "dark") {
      this._coreConfigService.setConfig(
        { layout: { skin: this.prevSkin ? this.prevSkin : "default" } },
        { emitEvent: true }
      );
    } else {
      localStorage.setItem("prevSkin", this.currentSkin);
      this._coreConfigService.setConfig(
        { layout: { skin: "dark" } },
        { emitEvent: true }
      );
    }
  }

  /**
   * Logout method
   */
  logout() {
    this._authenticationService.logout();
    // this._router.navigate(['/pages/authentication/login-v2']);
  }

  // Lifecycle Hooks
  // -----------------------------------------------------------------------------------------------------

  /**
   * On init
   */

  ngOnInit(): void {
    this.hideCmsLogo = this.shouldHideBrand(this.router.url);
    this.router.events 
      .pipe(filter(e => e instanceof NavigationEnd))
      .subscribe((e: NavigationEnd) => {
        const u = e.urlAfterRedirects || e.url;
        this.hideCmsLogo = this.shouldHideBrand(u);
        this.cdr.markForCheck();
      });

    // get the currentUser details from localStorage
    this._authenticationService.currentUser.subscribe((res) => {
      this.currentUser = res;
      this.userName = res?.fullname;
    });
    this.checkCommercialRoute(this.router.url);
    this.router.events
    .pipe(filter((e): e is NavigationEnd => e instanceof NavigationEnd))
    .subscribe(e => {
      this.checkCommercialRoute(e.urlAfterRedirects);
    });
    this.role = JSON.parse(localStorage.getItem("current_User"))?.role;
    this.isADUser = localStorage.getItem("isADUser") === "true";
    // Subscribe to the config changes
    this._coreConfigService.config
      .pipe(takeUntil(this._unsubscribeAll))
      .subscribe((config) => {
        this.coreConfig = config;
        this.horizontalMenu = config.layout.type === "horizontal";
        this.hiddenMenu = config.layout.menu.hidden === true;
        this.currentSkin = config.layout.skin;

        // Fix: for vertical layout if default navbar fixed-top than set isFixed = true
        if (this.coreConfig.layout.type === "vertical") {
          setTimeout(() => {
            if (this.coreConfig.layout.navbar.type === "fixed-top") {
              this.isFixed = true;
            }
          }, 0);
        }
      });

    // Horizontal Layout Only: Add class fixed-top to navbar below large screen
    if (this.coreConfig.layout.type == "horizontal") {
      // On every media(screen) change
      this._coreMediaService.onMediaUpdate
        .pipe(takeUntil(this._unsubscribeAll))
        .subscribe(() => {
          const isFixedTop = this._mediaObserver.isActive("bs-gt-xl");
          if (isFixedTop) {
            this.isFixed = false;
          } else {
            this.isFixed = true;
          }
        });
    }

    // Set the selected language from default languageOptions
    this.selectedLanguage = _.find(this.languageOptions, {
      id: this._translateService.currentLang,
    });
    this.formAddAccount = this.fb.group(
      {
        email: [null, [Validators.required, Validators.email]],
        fullName: [null, Validators.required],
        password: [
          null,
          [
            Validators.required,
            Validators.minLength(6),
            Validators.pattern(/^(?=.*[A-Z])(?=.*\d)(?=.*[\W_]).{6,}$/),
          ],
        ],
        confirm_password: ["", Validators.required],
      },
      {
        validator: this.mustMatch("password", "confirm_password"),
      }
    );
    this.formChangePass = this.fb.group(
      {
        current_password: [null, Validators.required],
        new_password: [
          null,
          [
            Validators.required,
            Validators.minLength(6),
            Validators.pattern(/^(?=.*[A-Z])(?=.*\d)(?=.*[\W_]).{6,}$/),
          ],
        ],
        confirm_password: ["", Validators.required],
      },
      {
        validator: this.mustMatch("new_password", "confirm_password"),
      }
    );
  }
  private checkCommercialRoute(url: string) {
    // chỉ true với /commercial hoặc /commercial/...
    this.isCommercialRoute = url.startsWith('/commercial');
  }
  mustMatch(controlName: string, matchingControlName: string) {
    return (formGroup: FormGroup) => {
      const control = formGroup.controls[controlName];
      const matchingControl = formGroup.controls[matchingControlName];

      if (matchingControl.errors && !matchingControl.errors.mustMatch) {
        return;
      }

      if (control.value !== matchingControl.value) {
        matchingControl.setErrors({ mustMatch: true });
      } else {
        matchingControl.setErrors(null);
      }
    };
  }
  modalOpen(modalSM) {
    this.modalService.open(modalSM, {
      centered: true,
      size: "sm",
    });
  }
  addAccount() {
    this.modalOpen(this.addAccountModal);
  }
  changePass() {
    this.modalOpen(this.changePassModal);
  }
  submitChangePass() {
    if (this.formChangePass.valid) {
      this.isPasswordChanging = true;
      this._authenticationService
        .changePass(
          this.formChangePass.get("current_password").value,
          this.formChangePass.get("new_password").value
        )
        .pipe(finalize(() => { this.isPasswordChanging = false; }))
        .subscribe(
          (res) => {
            if (res.code == 200) {
              this._toast.success("Đổi mật khẩu", "Thành công", {
                positionClass: "toast-top-right",
                toastClass: "toast ngx-toastr",
                closeButton: true,
              });
              this.modalService.dismissAll();
              this.formChangePass.reset();
            }
          },
          (err) => {
            this._toast.error("Sai mật khẩu", "Lỗi", {
              positionClass: "toast-top-right",
              toastClass: "toast ngx-toastr",
              closeButton: true,
            });
          }
        );
    }
  }
  submitAddAccount() {
    if (this.formAddAccount.valid) {
      this._authenticationService
        .addAccount(
          this.formAddAccount.get("email").value,
          this.formAddAccount.get("fullName").value,
          this.formAddAccount.get("password").value
        )
        .subscribe(
          (res) => {
            if (res) {
              this._toast.success("Tạo tài khoản", "Thành công", {
                positionClass: "toast-top-right",
                toastClass: "toast ngx-toastr",
                closeButton: true,
              });
              this.modalService.dismissAll();
              this.formAddAccount.reset();
            }
          },
          (err) => {
            if (err.error === "user with this email already exists.") {
              this._toast.error("Email đã được sử dụng", "Thất bại", {
                positionClass: "toast-top-right",
                toastClass: "toast ngx-toastr",
                closeButton: true,
              });
            } else {
              this._toast.error("Thêm tài khoản", "Thất bại", {
                positionClass: "toast-top-right",
                toastClass: "toast ngx-toastr",
                closeButton: true,
              });
            }
          }
        );
    }
  }
  report() {
    this.modalOpen(this.reportModal);
  }

  imagePreviews: string[] = [];
  formData: FormData = new FormData();
  addFile(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (!input.files) return;

    this.imagePreviews = []; // Clear previous previews (optional)

    Array.from(input.files).forEach((file) => {
      if (!file.type.startsWith("image/")) return;

      const reader = new FileReader();
      reader.onload = () => {
        if (reader.result) {
          this.imagePreviews.push(reader.result as string);
        }
      };
      reader.readAsDataURL(file);
      this.formData.append("attachments", file);
    });
  }
  editUser() {
    this.modalOpen(this.editUserModal);
  }
  refreshLinkAvatar(event) {
    this._authenticationService.refreshAvatar().subscribe((res) => {
      const avatarLink = res.data.avatar_url;
      (event.target as HTMLImageElement).src = avatarLink;
      let currentUser = JSON.parse(localStorage.getItem("current_User"));

      // Bước 2: Cập nhật giá trị email
      if (currentUser) {
        currentUser.avatar = avatarLink;

        // Bước 3: Ghi lại vào localStorage
        localStorage.setItem("current_User", JSON.stringify(currentUser));
      }
    });
  }
  /**
   * On destroy
   */
  ngOnDestroy(): void {
    // Unsubscribe from all subscriptions
    this._unsubscribeAll.next(null);
    this._unsubscribeAll.complete();
  }
  randomString(): string {
    return Math.random().toString(36).substring(2, 10);
  }
}
