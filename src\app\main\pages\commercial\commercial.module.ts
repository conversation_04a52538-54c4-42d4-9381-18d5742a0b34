import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { CommercialComponent } from './commercial.component';
import { CommercialChatComponent } from "./commercial-chat/commercial-chat.component";
import { FormsModule } from "@angular/forms";
import { NgbDropdownModule, NgbModalModule } from '@ng-bootstrap/ng-bootstrap';
import { CommercialSearchComponent } from './commercial-search/commercial-search.component';
import { ViewDetailFileModule } from 'app/layout/components/view-detail-file/view-detail-file.module';

@NgModule({
  declarations: [
    CommercialComponent,
    CommercialChatComponent,
    CommercialSearchComponent,
  ],
  imports: [
    CommonModule,
    FormsModule,
    RouterModule,
    NgbDropdownModule,
    NgbModalModule,
    ViewDetailFileModule,
  ],
  exports: [CommercialComponent]
})
export class CommercialModule {}
