import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ement<PERSON><PERSON>, QueryList, } from "@angular/core";
import { environment } from "environments/environment";
import { <PERSON><PERSON>anitizer, SafeHtml } from "@angular/platform-browser";
import { marked } from "marked";

import { AuthenticationService } from "app/auth/service";
import { Subject } from "rxjs";
import { takeUntil } from "rxjs/operators";
type Role = "user" | "assistant";

interface ChatSource {
  title: string;
  type: "web" | "org" | "personal";
  page?: string;
}

interface ChatMessage {
  id?: string;
  role: Role;
  content: string;
  sources?: ChatSource[];
}

interface ChatSession {
  id: number;
  title: string;
  date: string;
  messages: ChatMessage[];
  backendConversationId?: string | null;
}

@Component({
  selector: "app-commercial-chat",
  templateUrl: "./commercial-chat.component.html",
  styleUrls: ["./commercial-chat.component.scss"],
})
export class CommercialChatComponent {
  @ViewChildren('titleInput') titleInputs!: <PERSON>ry<PERSON><PERSON><ElementRef<HTMLInputElement>>;

  // danh sách phiên làm việc
  sessions: ChatSession[] = [
    {
      id: 1,
      title: "Phiên làm việc mới",
      date: "Hôm nay",
      messages: [
        {
          role: "assistant",
          content:
            "Xin chào, em là trợ lý pháp chế AI. Anh cần hỗ trợ vấn đề gì?",
        },
      ],
    },
  ];
  editingSessionId: number | null = null;
  editTitle = "";
  activeSessionId = 1;
  currentUser: any;
  userName = "";
  userInitials = "MN";

  private _destroy$ = new Subject<void>();
  // toggle tool bar
  useRepo = true;
  useGoogle = false;
  modelMode: "auto" | "c-legal" = "auto";

  // chọn vai trò
  roles = ["Trợ lý Pháp chế chung", "Chuyên gia Hợp đồng"];
  selectedRole = this.roles[0];
  private readonly STORAGE_KEY = "commercial_chat_sessions";

  private readonly commercialWorkspaceId =
    "54693e01-7120-4216-b9f2-ccb29d7cd15e";

  // nếu sau này có upload file thì sẽ bind vào 2 biến này
  private selectedUploadFiles: any[] = [];
  private selectedSaveFiles: any[] = [];
  private selectionText: string | null = null;

  // input
  inputValue = "";
  conversationId: string | null = null;
  isStreaming = false;
  thinkingStatus = "";
  private abortController: AbortController | null = null;
  // follow-up demo
  followUps: string[] = [
    "Tạo checklist tuân thủ mua sắm?",
    "Quy định về hạn mức chỉ định thầu?",
    "Mẫu tờ trình phê duyệt nhà thầu?",
  ];

  // gợi ý quick card ở empty state
  quickSuggestions = [
    "Rà soát rủi ro tuân thủ trong quy trình mua sắm?",
    "Quy định về quà tặng đối tác trong dịp Tết?",
    "Cập nhật tiến độ vụ kiện tranh chấp HĐ với Công ty X?",
    "Đánh giá tác động của AI đến khối Tín dụng và AML?",
  ];

  get activeSession(): ChatSession | undefined {
    return this.sessions.find((s) => s.id === this.activeSessionId);
  }

  constructor(
    private sanitizer: DomSanitizer,
    private authService: AuthenticationService
  ) {
    this.authService.currentUser
      .pipe(takeUntil(this._destroy$))
      .subscribe((user) => {
        this.currentUser = user;
        this.userName = user?.fullname || "CLS User";
        this.userInitials = this.buildInitials(this.userName);
      });
  }

  ngOnInit(): void {
    this.loadSessionsFromStorage();
  }
  startRename(session: ChatSession, event: MouseEvent): void {
    event.stopPropagation();
    this.editingSessionId = session.id;
    this.editTitle = session.title;
    setTimeout(() => {
      const inputEl = this.titleInputs?.last?.nativeElement;
      if (inputEl) {
        inputEl.focus();
        const len = inputEl.value.length;
        inputEl.setSelectionRange(len, len);
      }
    });
  }

  confirmRename(session: ChatSession): void {
    if (this.editingSessionId !== session.id) return;

    const newTitle = this.editTitle.trim();
    if (newTitle && newTitle !== session.title) {
      session.title = newTitle;
      this.persistSessions();
    }

    this.editingSessionId = null;
  }
  private loadSessionsFromStorage(): void {
    try {
      const raw = localStorage.getItem(this.STORAGE_KEY);
      if (!raw) return;

      const data: ChatSession[] = JSON.parse(raw);

      if (data && data.length) {
        this.sessions = data;
        // chọn phiên đầu tiên đã lưu
        this.activeSessionId = data[0].id;
        const active = data.find((s) => s.id === this.activeSessionId);
        this.conversationId = active?.backendConversationId || null;
      }
    } catch (e) {
      console.error("Failed to load sessions:", e);
    }
  }

  private persistSessions(): void {
    try {
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(this.sessions));
    } catch (e) {
      console.error("Failed to save sessions:", e);
    }
  }

  private buildInitials(name: string): string {
    if (!name) return "U";
    return name
      .trim()
      .split(/\s+/)
      .slice(-2)
      .map((p) => p[0])
      .join("")
      .toUpperCase();
  }

  private cleanModelChunk(chunk: string): string {
    if (!chunk) return "";

    let text = chunk;

    // Bỏ toàn bộ khối <think>...</think> nếu BE có trả về
    text = text.replace(/<think>[\s\S]*?<\/think>/g, "");

    // Bỏ mọi <answer> và </answer> còn sót lại
    text = text.replace(/<answer>/g, "");
    text = text.replace(/<\/answer>/g, "");

    return text;
  }

  startNewSession(): void {
    const maxId = this.sessions.length
      ? Math.max(...this.sessions.map((s) => s.id))
      : 0;
    const id = maxId + 1;

    const greeting: ChatMessage = {
      role: "assistant",
      content: "Xin chào, em là trợ lý pháp chế AI. Anh cần hỗ trợ vấn đề gì?",
    };

    const session: ChatSession = {
      id,
      title: `Phiên làm việc ${id}`,
      date: "Vừa xong",
      messages: [greeting],
      backendConversationId: null,
    };

    this.sessions = [session, ...this.sessions];
    this.activeSessionId = id;
    this.conversationId = null;

    this.persistSessions();
  }

  selectSession(id: number): void {
    this.activeSessionId = id;
    const s = this.sessions.find((x) => x.id === id);
    this.conversationId = s?.backendConversationId || null;
  }

  sendFromSuggestion(text: string): void {
    this.sendMessage(text);
  }

  sendFromFollowUp(text: string): void {
    this.sendMessage(text);
  }

  async sendMessage(text?: string): Promise<void> {
    const value = (text ?? this.inputValue).trim();
    const session = this.activeSession;
    if (!value || !session) return;

    const userMsg: ChatMessage = {
      role: "user",
      content: value,
    };
    session.messages.push(userMsg);

    if (session.messages.length === 2) {
      const shortTitle =
        value.length > 40 ? value.slice(0, 37).trimEnd() + "..." : value;
      session.title = shortTitle;
      session.date = "Hôm nay"; // hoặc format ngày giờ thực tế
    }

    const aiMsg: ChatMessage = {
      role: "assistant",
      content: "",
      sources: [],
    };
    session.messages.push(aiMsg);

    this.inputValue = "";
    this.followUps = [];

    // lưu tạm trước khi stream
    this.persistSessions();

    await this.streamFromBackend(session, aiMsg);

    // lưu sau khi có câu trả lời
    this.persistSessions();
  }

  renameSession(session: ChatSession): void {
    const newTitle = prompt("Nhập tên đoạn chat", session.title);
    if (newTitle && newTitle.trim()) {
      session.title = newTitle.trim();
      this.persistSessions();
    }
  }

  private extractJsonObjects(str: string): [any[], string] {
    const objects: any[] = [];
    let depth = 0;
    let start = -1;
    let inString = false;
    let escape = false;

    for (let i = 0; i < str.length; i++) {
      const char = str[i];

      if (char === '"' && !escape) {
        inString = !inString;
      }

      if (!inString) {
        if (char === "{") {
          if (depth === 0) start = i;
          depth++;
        } else if (char === "}") {
          depth--;
          if (depth === 0 && start !== -1) {
            const jsonStr = str.slice(start, i + 1);
            try {
              objects.push(JSON.parse(jsonStr));
            } catch (e) {
              console.warn("JSON parse failed:", jsonStr);
            }
            start = -1;
          }
        }
      }

      escape = char === "\\" && !escape;
    }

    const remaining =
      depth > 0 && start !== -1 ? str.slice(start) : "";
    return [objects, remaining];
  }

  private async streamFromBackend(
    session: ChatSession,
    aiMsg: ChatMessage
  ): Promise<void> {
    this.abortController = new AbortController();
    this.isStreaming = true;
    this.thinkingStatus = "Đang tìm kiếm tài liệu liên quan...";
    this.followUps = [];

    // ====== CHUẨN HÓA messages ======
    let messagesForApi = [...session.messages];

    // Bỏ assistant placeholder rỗng ở cuối (aiMsg),
    // BE chỉ cần history thực, kết thúc bằng user.
    if (
      messagesForApi.length &&
      messagesForApi[messagesForApi.length - 1].role === "assistant" &&
      !messagesForApi[messagesForApi.length - 1].content?.trim()
    ) {
      messagesForApi = messagesForApi.slice(0, -1);
    }

    // if (
    //   messagesForApi.length &&
    //   messagesForApi[0].role === "assistant" &&
    //   messagesForApi[0].content.startsWith("Xin chào, em là trợ lý pháp chế AI")
    // ) {
    //   messagesForApi = messagesForApi.slice(1);
    // }

    const bodyMessages = messagesForApi.map((m) => ({
      role: m.role,
      content: m.content,
    }));
    // ==============================================

    try {
      const resp = await fetch(`${environment.apichatbot}/chat`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${localStorage.getItem("token") || ""}`,
        },
        body: JSON.stringify({
          messages: bodyMessages,
          do_rewrite: false,
          stream: true,
          // giống chatbot component:
          workspace_id: this.commercialWorkspaceId,
          conversation_id: this.conversationId,
          use_law_database: this.useRepo,
          use_google_search: this.useGoogle,
          creative_mode: this.modelMode === "auto" ? false : true,
          selected_save_files: this.selectedSaveFiles,
          selected_upload_files: this.selectedUploadFiles,
          selection_text: this.selectionText,
        }),
      });

      if (!resp.body) {
        console.error("No response body from /chat");
        return;
      }

      const reader = resp.body.getReader();
      const decoder = new TextDecoder("utf-8");
      let buffer = "";
      let answerText = "";

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const [objects, remaining] = this.extractJsonObjects(buffer);
        buffer = remaining;

        for (const obj of objects) {
          // console.log("STREAM OBJ >>>", obj);
          // cập nhật status/thinking
          if (obj.status && obj.data?.message) {
            this.thinkingStatus = obj.data.message;
          }

          if (obj.conversation_id && !this.conversationId) {
            this.conversationId = obj.conversation_id;
          }

          if (obj.text) {
            let contentChunk: string = obj.text;

            // cắt <answer> nếu có
            const idx = contentChunk.indexOf("<answer>");
            if (idx !== -1) {
              contentChunk = contentChunk.slice(idx + "<answer>".length);
            }

            // xoá <think>, <answer>, ...
            contentChunk = this.cleanModelChunk(contentChunk);

            // NẾU ĐÃ CÓ TEXT THẬT SỰ → ẨN DÒNG "Đang chuẩn bị câu trả lời..."
            if (contentChunk.trim().length > 0) {
              this.thinkingStatus = "";
            }

            answerText += contentChunk;
            aiMsg.content = answerText;
          }

          if (obj.data?.followups && Array.isArray(obj.data.followups)) {
            this.followUps = obj.data.followups;
          }

          if (obj.data?.sources && Array.isArray(obj.data.sources)) {
            aiMsg.sources = obj.data.sources.map((s: any) => ({
              title: s.title,
              type: s.type || "web",
              page: s.page,
            }));
          }
        }
      }
    } catch (e) {
      console.error("streamFromBackend error:", e);
    } finally {
      this.isStreaming = false;
      this.thinkingStatus = "";
      this.abortController = null;
      this.persistSessions();
    }
  }

  renderMessageContent(text: string): SafeHtml {
    const raw = text || "";
    // chuyển markdown thành HTML
    const html = marked.parse(raw) as string;
    return this.sanitizer.bypassSecurityTrustHtml(html);
  }

  handleKeyDown(event: KeyboardEvent): void {
    if (event.key === "Enter" && !event.shiftKey) {
      event.preventDefault();
      this.sendMessage();
    }
  }
  ngOnDestroy(): void {
    this._destroy$.next();
    this._destroy$.complete();
  }
}
