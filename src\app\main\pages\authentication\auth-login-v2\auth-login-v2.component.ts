import { Component, OnInit, ViewEncapsulation } from "@angular/core";
import { ActivatedRoute } from "@angular/router";
import { FormBuilder, FormGroup, Validators } from "@angular/forms";
import { catchError, takeUntil, take } from "rxjs/operators";
import { Subject, Subscription, of } from "rxjs";
import { Router } from "@angular/router";
import { CoreConfigService } from "@core/services/config.service";
import { AuthenticationService } from "app/auth/service";
import { ToastrService } from "ngx-toastr";
import { WebSocketService } from "app/auth/service/webSocket.service";
import { environment } from "environments/environment";
import { DocumentStatus } from "app/models/DocumentStatus";
import { ChangeDataService } from "app/auth/service/change-data.service";
import { getLogoImage as getLogoImageHelper, getAppName as getAppNameHelper, DEFAULT_LOGO_IMAGE, setLoginBackground, isVPQHDomain } from "app/shared/image.helper";
import { CmsService } from 'app/main/apps/cms/cms.service';
import { SocialAuthService, GoogleLoginProvider, SocialUser } from '@abacritt/angularx-social-login';
import { HttpClient } from '@angular/common/http';
import Swal from "sweetalert2";

@Component({
  selector: "app-auth-login-v2",
  templateUrl: "./auth-login-v2.component.html",
  styleUrls: ["./auth-login-v2.component.scss"],
  encapsulation: ViewEncapsulation.None,
})
export class AuthLoginV2Component implements OnInit {
  //  Public
  public activeTab: 'login' | 'register' = 'login';
  public coreConfig: any;
  public loginForm: FormGroup;
  public loading = false;
  public submitted = false;
  public returnUrl: string;
  public error = "";
  public passwordTextType: boolean;
  private messageSubscription!: Subscription;
  getAppName = getAppNameHelper
  public defaultLogo = DEFAULT_LOGO_IMAGE;
  public readonly isVPQHBrand = isVPQHDomain();
  public readonly guideDocumentUrl =
    "https://drive.google.com/drive/folders/1kMQUNE7zD7K277-F6Kh7dnl6rh8NqpWC";
  public readonly hotline = "********** | 08048619";

  public documentStatus = DocumentStatus;

  // Social Login
  public user: SocialUser | null = null;
  public loggedIn = false;


  // Private
  private _unsubscribeAll: Subject<any>;
  private authSub?: Subscription;

  /**
   * Constructor
   *
   * @param {CoreConfigService} _coreConfigService
   */
  constructor(
    private _coreConfigService: CoreConfigService,
    private _formBuilder: FormBuilder,
    private _route: ActivatedRoute,
    private _router: Router,
    private _authenService: AuthenticationService,
    private webSocketService: WebSocketService,
    private changeData: ChangeDataService,
    private toast: ToastrService,
    private cmsService: CmsService,
    private _socialAuthService: SocialAuthService, 
    private http: HttpClient,
  ) {
    this._unsubscribeAll = new Subject();

    // Configure the layout
    this._coreConfigService.config = {
      layout: {
        navbar: {
          hidden: true,
        },
        menu: {
          hidden: true,
        },
        footer: {
          hidden: true,
        },
        customizer: false,
        enableLocalStorage: false,
      },
    };
  }

  // convenience getter for easy access to form fields
  get f() {
    return this.loginForm.controls;
  }

  prepareLoginGoogle() {
    this.authSub = this._socialAuthService.authState.subscribe((user) => {
      this.user = user;
      if (user) {
        this._authenService.login(user.idToken)
        .subscribe(
          (res) => {
            this._router.navigate(["/"]);
            const token = res.access;
            if (token) {
              this.webSocketService.connect(`${environment.apiSocket}/notifications?token=${token}`);
              this.messageSubscription = this.webSocketService.messageSubject
                .pipe(takeUntil(this._unsubscribeAll))
                .subscribe((message) => {
                  var dataMsg = message?.data;
                  if (dataMsg) {
                    if (dataMsg.data.status == DocumentStatus.STATUS_SUCCESS) {
                      this.toast.success(
                        `Quá trình ${message.event} tài liệu ${dataMsg.data.name} thành công`,
                        dataMsg.data.status_display,
                        {
                          closeButton: true,
                          positionClass: "toast-top-right",
                          toastClass: "toast ngx-toastr",
                        }
                      );
                    } else if (
                      dataMsg.data.status == DocumentStatus.STATUS_PROCESSING
                    ) {
                      this.toast.info(
                        `Đang xử lý ${message.event} tài liệu ${dataMsg.data.name}`,
                        dataMsg.data.status_display,
                        {
                          closeButton: true,
                          positionClass: "toast-top-right",
                          toastClass: "toast ngx-toastr",
                        }
                      );
                    } else {
                      this.toast.error(
                        `Quá trình ${message.event} tài liệu ${dataMsg.data.name} thất bại`,
                        dataMsg.data.status_display,
                        {
                          closeButton: true,
                          positionClass: "toast-top-right",
                          toastClass: "toast ngx-toastr",
                        }
                      );
                    }
                    this.changeData.changeData.next(true);
                  }
                });
            }
            const hasCommercial = !!res?.has_commercial;
            if (hasCommercial) {
              this._router.navigateByUrl('/commercial', { replaceUrl: true });
            } else {
              this._router.navigateByUrl('/', { replaceUrl: true });
            }
          },
          (err) => {
            this.loading = false;
            if (err.error == "User social does not exist") {
              this.activeTab = "register"
            }
            if (err.error == "RegisterQueue") {
              this._socialAuthService.signOut();
              this.error = err.message;
            }
          }
        );
      }
    });
  }
  
  /**
   * Toggle password
   */
  togglePasswordTextType() {
    this.passwordTextType = !this.passwordTextType;
  }

  getLogoImage(): string {
    return getLogoImageHelper();
  }

  onTabChange(event) {
    this.error = "";
    this.activeTab = event;
    if (event == 'login') {
      this._socialAuthService.signOut();
    }
  }

  onSubmit(event?: Event) {
    // Prevent default form submission to avoid page reload
    if (event) {
      event.preventDefault();
    }
    this.submitted = true;
    // stop here if form is invalid
    if (this.loginForm.invalid) {
      return;
    }
    // Login
    this.loading = true;

    // redirect to home page
    this._authenService.login(
      this.loginForm.get('email').value,
      this.loginForm.get('password').value
    ).subscribe(
      (res) => {
        const token = res?.access;
        if (token) {
          this.webSocketService.connect(`${environment.apiSocket}/notifications?token=${token}`);
        }
        const hasCommercial = !!res?.has_commercial;

        if (hasCommercial) {
          // Nếu có quyền commercial -> đi thẳng vào /commercial và dừng luôn
          this._router.navigateByUrl('/commercial', { replaceUrl: true });
          this.loading = false;
          return;
        }
        //tự động vào CMS khi có role:
        const rows = JSON.parse(localStorage.getItem('cms_roles') || '[]') as any[];
        const active = (rows || []).filter(r => r?.is_active).map(r => r?.role as string);
        const hasCms = ['WRITER','REVIEWER','ADMIN'].some(r => active.includes(r));

        // Điều hướng mặc định:
        this._router.navigateByUrl('/', { replaceUrl: true });

        // Nếu có quyền CMS thì đẩy sang CMS dashboard
        if (hasCms) {
          this._router.navigateByUrl('/cms/dashboard', { replaceUrl: true });
        }

        this.loading = false;
      },
      (err) => {
        this.loading = false;
        if (err?.error === 'RegisterQueue') this.error = err.message;
        if (err?.code == 213) {
          Swal.fire({
            icon: 'warning',
            title: 'Yêu cầu đặt lại mật khẩu',
            text: 'Tài khoản của bạn cần được đặt lại mật khẩu để tiếp tục sử dụng. Vui lòng thực hiện đặt lại mật khẩu.',
            showCancelButton: true,
            confirmButtonText: 'Đặt lại mật khẩu',
            cancelButtonText: 'Huỷ'
          }).then((result) => {
            if (result.isConfirmed) {
              this._router.navigate(
                ['/pages/authentication/forgot-password-v2'], 
                { queryParams: { email: this.loginForm.get('email').value } }
              );
            }
          });
        }
      }
    );
  }

  // Lifecycle Hooks
  // -----------------------------------------------------------------------------------------------------

  /**
   * On init
   */
  ngOnInit(): void {
    localStorage.removeItem("current_User");
    this.loginForm = this._formBuilder.group({
      email: ["", [Validators.required]],
      password: ["", Validators.required],
    });

    // get return url from route parameters or default to '/'
    this.returnUrl = this._route.snapshot.queryParams["returnUrl"] || "/";

    // set background image
    setLoginBackground();

    // Subscribe to config changes
    this._coreConfigService.config
      .pipe(takeUntil(this._unsubscribeAll))
      .subscribe((config) => {
        this.coreConfig = config;
      });

    this.prepareLoginGoogle();
  }

  /**
   * On destroy
   */
  ngOnDestroy(): void {
    // Unsubscribe from all subscriptions
    this._unsubscribeAll.next(null);
    this._unsubscribeAll.complete();
    this.authSub?.unsubscribe();
  }
  goHome() {
    this._router.navigate(['/home']);
  }

}
