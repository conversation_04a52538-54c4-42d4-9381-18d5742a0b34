import {
  <PERSON>mpo<PERSON>,
  <PERSON><PERSON>ni<PERSON>,
  ViewChild,
  Template<PERSON>ef,
  AfterViewInit,
  AfterViewChecked,
} from "@angular/core";
import { Router } from "@angular/router";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { environment } from "environments/environment";

import { ViewDetailFileService } from "app/layout/components/view-detail-file/view-detail-file.service";
import { ListDocumentService } from "app/main/apps/quan-ly-van-ban/detail-work-space/list-document/list-document.service";
import { ShowContent } from "app/models/ShowContent";
import { FormType } from "app/models/FormType";
import * as feather from 'feather-icons';
import { HttpClient, HttpHeaders } from "@angular/common/http";
import { InterceptorSkipHeader } from "@core/components/loading/loading.interceptor";

interface DocType {
  label: string;
  count: number;
  checked: boolean;
}

interface FileItem {
  id?: number | string;
  name: string;
  size: string;
  date: string;
  tag: string;
  raw?: any;
}

interface EsLoaiVanBan {
  key: string;
  doc_count: number;
}

interface SearchByQueryResponse {
  status: number;
  msg: string;
  data: any[];
  total: number;
  statistic: { label: string; count: number }[];
  is_fallback: boolean;
  is_paging: boolean;
}

@Component({
  selector: "app-commercial-search",
  templateUrl: "./commercial-search.component.html",
  styleUrls: ["./commercial-search.component.scss"],
})
export class CommercialSearchComponent implements OnInit {
  // ====== MODAL XEM TOÀN VĂN ======
  @ViewChild("viewFileModal") viewFileModal!: TemplateRef<any>;
  public FormType = FormType;
  public types: FormType;

  // sidebar “Loại văn bản”
  docTypes: DocType[] = [];

  activeTab: "org" | "personal" = "org";
  searchKeyword = "";

  orgFiles: FileItem[] = [];
  personalFiles: FileItem[] = [];

  pageSize = 10;
  currentPage = 1;

  isLoading = false;
  hasSearched = false;
  totalResults = 0;

  constructor(
    private router: Router,
    private http: HttpClient,
    private modalService: NgbModal,
    private viewDetailFileService: ViewDetailFileService,
    private listDocumentService: ListDocumentService
  ) {}

  ngOnInit(): void {
    this.loadDocTypes();
  }
  
  ngAfterViewInit(): void {
    feather.replace();
  }

  ngAfterViewChecked(): void {
    feather.replace();
  }

  private loadDocTypes(): void {
    const url = `${environment.apiUrl}/dashboard/es-loai-van-ban-stats/`;

    this.http.post<EsLoaiVanBan[]>(url, {}).subscribe({
      next: (res) => {
        this.docTypes = res.map((item, index) => ({
          label: item.key,
          count: item.doc_count,
          checked: index === 0,
        }));
      },
      error: (err) => {
        console.error("loadDocTypes error", err);
        if (!this.docTypes.length) {
          this.docTypes = [{ label: "Tất cả", count: 0, checked: true }];
        }
      },
    });
  }

  get totalDocs(): number {
    const allType = this.docTypes.find((t) => t.label === "Tất cả");
    return allType ? allType.count : 0;
  }

  onSubmitSearch(): void {
    const keyword = this.searchKeyword.trim();
    this.currentPage = 1;

    // chỉ coi là đã tìm khi có keyword
    this.hasSearched = !!keyword;

    if (!keyword) {
      this.orgFiles = [];
      this.personalFiles = [];
      this.totalResults = 0;
      this.isLoading = false;
      return;
    }

    this.orgFiles = [];
    this.personalFiles = [];
    this.isLoading = true;

    this.searchByQuery(keyword).subscribe({
      next: (res) => {
        this.totalResults = res.total || (res.data ? res.data.length : 0);
        this.orgFiles = this.mapResultsToFiles(res.data || []);
        this.isLoading = false;
      },
      error: (err) => {
        console.error("searchByQuery error", err);
        this.orgFiles = [];
        this.totalResults = 0;
        this.isLoading = false;
      },
    });
  }

  private searchByQuery(keyword: string) {
    const url = `${environment.apiUrl}/legal_search/by_query`;
    const body = { query: keyword };

    const headers = new HttpHeaders().set(InterceptorSkipHeader, "");

    return this.http.post<SearchByQueryResponse>(url, body, { headers });
  }


  private mapResultsToFiles(results: any[]): FileItem[] {
    return results.map((item) => ({
      id: item.id,
      name: item.title || item.trich_yeu || "Văn bản không tên",
      size: "—",
      date: this.formatDate(item.ngay_ban_hanh || item.ngay_co_hieu_luc),
      tag: item.loai_van_ban || item.label || "—",
      raw: item,
    }));
  }

  private formatDate(dateStr?: string): string {
    if (!dateStr) return "";
    const d = new Date(dateStr);
    if (isNaN(d.getTime())) return dateStr;
    return d.toLocaleDateString("vi-VN");
  }

  // ================== GETTER BẢNG ==================
  get files(): FileItem[] {
    const source = this.activeTab === "org" ? this.orgFiles : this.personalFiles;
    let result = [...source];

    const allType = this.docTypes[0];
    const activeDocTypes = this.docTypes
      .filter((t) => t.checked && t.label !== "Tất cả")
      .map((t) => t.label.toLowerCase());

    if (allType && !allType.checked && activeDocTypes.length > 0) {
      result = result.filter((f) =>
        activeDocTypes.includes((f.tag || "").toLowerCase())
      );
    }

    return result;
  }

  get totalPages(): number {
    return Math.max(1, Math.ceil(this.files.length / this.pageSize));
  }

  get pagedFiles(): FileItem[] {
    const start = (this.currentPage - 1) * this.pageSize;
    return this.files.slice(start, start + this.pageSize);
  }

  // ================== CLEAR / TAB / PAGE ==================
  clearSearch(): void {
    this.searchKeyword = "";
    this.currentPage = 1;
    this.orgFiles = [];
    this.personalFiles = [];
    this.totalResults = 0;
    this.hasSearched = false;
  }

  changeTab(tab: "org" | "personal"): void {
    if (this.activeTab !== tab) {
      this.activeTab = tab;
      this.currentPage = 1;
    }
  }

  gotoPage(page: number): void {
    if (page < 1 || page > this.totalPages) return;
    this.currentPage = page;
  }

  // ================== MỞ POPUP TOÀN VĂN ==================
  viewFile(file: FileItem): void {
    console.log("viewFile click:", file);

    const raw = file.raw || file;

    // Đẩy dữ liệu cho view-detail-file & list-document
    this.viewDetailFileService.fileInfor.next(raw);
    this.viewDetailFileService.clauseId2.next(null);
    this.listDocumentService.FileSearchTemp.next(raw);
    this.listDocumentService.setBehavior(ShowContent.Search);

    // Cập nhật queryParams
    this.router.navigate([], {
      queryParams: {
        fileId: file.id,
        tabs: "toanvan",
        type: "searching",
        time: new Date().getTime(),
        fileName: file.name,
        save: true,
      },
      queryParamsHandling: "merge",
    });

    // Mở modal
    this.modalOpen(this.viewFileModal, FormType.Search, "xl");
  }
  get visiblePages(): number[] {
    const total = this.totalPages;
    const current = this.currentPage;

    // Nếu tổng số trang <= 3 thì show hết
    if (total <= 3) {
      return Array.from({ length: total }, (_, i) => i + 1);
    }

    // Đang ở rất gần đầu: 1 hoặc 2 -> luôn hiện 1,2,3
    if (current <= 2) {
      return [1, 2, 3];
    }

    // Đang ở rất gần cuối: trang cuối hoặc kế cuối -> hiện total-2, total-1, total
    if (current >= total - 1) {
      return [total - 2, total - 1, total];
    }

    // Còn lại: current ở giữa
    return [current - 1, current, current + 1];
  }

  get pageNumbers(): number[] {
    const total = this.totalPages;
    const maxVisible = 10;

    if (total <= maxVisible) {
      return Array.from({ length: total }, (_, i) => i + 1);
    }

    const half = Math.floor(maxVisible / 2);
    let start = this.currentPage - half;
    let end = this.currentPage + half - 1;

    if (start < 1) {
      start = 1;
      end = maxVisible;
    } else if (end > total) {
      end = total;
      start = total - maxVisible + 1;
    }

    const pages: number[] = [];
    for (let p = start; p <= end; p++) {
      pages.push(p);
    }
    return pages;
  }

  modalOpen(
    modalTpl: TemplateRef<any>,
    type: FormType,
    size: "sm" | "lg" | "xl" | string
  ) {
    this.types = type;
    this.modalService.open(modalTpl, {
      centered: true,
      size,
      backdrop: "static",
      keyboard: true,
      scrollable: true,
    });
  }

  // ================== CHECKBOX LOẠI VB ==================
  toggleDocType(type: DocType, event: Event): void {
    const input = event.target as HTMLInputElement;
    const checked = input.checked;

    if (type.label === "Tất cả") {
      this.docTypes.forEach(
        (t) => (t.checked = t.label === "Tất cả" ? checked : false)
      );
    } else {
      type.checked = checked;
      const hasAnySpecific = this.docTypes.some(
        (t) => t.label !== "Tất cả" && t.checked
      );
      const allType = this.docTypes.find((t) => t.label === "Tất cả");
      if (allType) {
        allType.checked = !hasAnySpecific;
      }
    }

    this.currentPage = 1;
  }
}
