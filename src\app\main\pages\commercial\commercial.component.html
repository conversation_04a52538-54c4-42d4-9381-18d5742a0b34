<div class="commercial-layout">
  <!-- SIDEBAR -->
  <aside
    class="commercial-sidebar"
    [class.commercial-sidebar--collapsed]="isSidebarCollapsed"
  >
    <button
      class="commercial-sidebar__toggle"
      type="button"
      (click)="toggleSidebar()"
    >
      <!-- Đang thu gọn: hiện mũi tên sang phải -->
      <span *ngIf="isSidebarCollapsed">›</span>
      <!-- Đang mở: hiện mũi tên sang trái -->
      <span *ngIf="!isSidebarCollapsed">‹</span>
    </button>

    <!-- Logo + title -->
    <div class="commercial-sidebar__brand">
      <div class="commercial-sidebar__logo">
        <img
          src="assets/images/logo/COpenAIlogo.svg"
          alt="C-OpenAI Logo"
          class="commercial-sidebar__logo-img"
        />
      </div>

      <div class="commercial-sidebar__title" *ngIf="!isSidebarCollapsed">
        <div class="commercial-sidebar__title-main">CLS</div>
        <div class="commercial-sidebar__title-sub">
          AI FOR LEGAL SERVICES
        </div>
      </div>
    </div>

    <!-- Menu -->
    <nav class="commercial-sidebar__menu">
      <button
        *ngFor="let item of navItems"
        type="button"
        class="commercial-sidebar__item"
        [class.commercial-sidebar__item--active]="activeTab === item.id"
        (click)="setActiveTab(item.id)"
      >
        <span
          class="commercial-sidebar__item-icon"
          [attr.data-feather]="item.icon"
        ></span>

        <span
          class="commercial-sidebar__item-label"
          *ngIf="!isSidebarCollapsed"
        >
          {{ item.label }}
        </span>
      </button>
    </nav>

    <!-- User mini card -->
    <div class="commercial-sidebar__user" *ngIf="!isSidebarCollapsed">
      <div class="commercial-sidebar__avatar">
        {{ userInitials }}
      </div>

      <div class="commercial-sidebar__user-info">
        <div class="commercial-sidebar__user-name">
          {{ userName || "CLS User" }}
        </div>
        <div class="commercial-sidebar__user-plan">
          Pro Plan
        </div>
      </div>

      <!-- Menu bánh răng (dropdown) -->
      <div ngbDropdown class="commercial-sidebar__user-menu">
        <button
          class="commercial-sidebar__settings-btn"
          type="button"
          ngbDropdownToggle
        >
          ⚙
        </button>
        <div ngbDropdownMenu>
          <button ngbDropdownItem (click)="logout()">
            <span [data-feather]="'power'" class="mr-50"></span>
            Đăng xuất
          </button>
        </div>
      </div>
    </div>
  </aside>

  <!-- MAIN -->
  <section class="commercial-main">
    <!-- <header class="commercial-header">
      <div class="commercial-header__left">
        <h1 class="commercial-header__title">CLS Commercial</h1>
        <p class="commercial-header__subtitle">
          Hệ thống trợ lý pháp chế AI (demo) cho khu vực Commercial.
        </p>
      </div>

      <div class="commercial-header__right">
        <a routerLink="/home" class="commercial-header__link">
          Về trang Landing
        </a>
      </div>
    </header> -->

    <div class="commercial-content" [ngSwitch]="activeTab">
      <!-- Tìm kiếm (placeholder) -->
      <div *ngSwitchCase="'search'" class="commercial-tab" [@tabFadeSlide]>
        <app-commercial-search></app-commercial-search>
      </div>

      <!-- Trợ lý Hỏi đáp – dùng component mới -->
      <div
        *ngSwitchCase="'chat'"
        class="commercial-tab commercial-tab--full"
        [@tabFadeSlide]
      >
        <app-commercial-chat></app-commercial-chat>
      </div>

      <!-- Default -->
      <div *ngSwitchDefault class="commercial-tab" [@tabFadeSlide]>
        Chọn một mục bên trái để bắt đầu.
      </div>
    </div>
  </section>
</div>
