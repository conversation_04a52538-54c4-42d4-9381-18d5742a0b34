@import '@core/scss/base/bootstrap-extended/include'; // Bootstrap includes
@import '@core/scss/base/components/include'; // Components includes

// To open dd on right
.dropdown-menu-right {
  right: 0 !important;
  left: auto !important;
}

// Cart Touchspin
.touchspin-cart {
  .touchspin-wrapper {
    width: 6.4rem;
  }
  &:focus-within {
    box-shadow: none !important;
  }
}

app-navbar-bookmark {
  display: flex;
}

.navbar-logo-cmc {
  width: 50px;
  height: 50px;
  margin-left: 1rem;
  object-fit: contain;
}

.navbar-divider {
  border-right: 1px solid rgba(224, 224, 224, 1);
  height: 40px;
  display:inline-block
}

.navbar-title-custom {
  font-weight: 700;
}

// Apply style on window scroll for navbar static type
.navbar-static-style-on-scroll {
  background-color: #fff !important;
  box-shadow: rgba(0, 0, 0, 0.05) 0px 4px 20px 0px !important;
}

// Dark Layout
.dark-layout {
  .navbar-container {
    .search-input {
      .search-list {
        li {
          &.auto-suggestion {
            &:hover {
              background-color: $theme-dark-body-bg;
            }
          }
        }
      }
    }
  }
}
.navbar-commercial {
  height: 60px;
  background-color: #ffffff;
  border-bottom: 1px solid #e5e5e5;
}
