import { HttpClient, HttpHeaders } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { InterceptorSkipHeader } from "@core/components/loading/loading.interceptor";
import { environment } from "environments/environment";
import { BehaviorSubject, Subject } from "rxjs";

// ✅ thêm trên đầu file (sau import) nếu anh muốn export dùng nơi khác
export interface LocalDoc {
  id: string;
  name: string;
  trich_yeu: string;
  toan_van: string;
  loai_van_ban?: string;
  so_hieu?: string;
  co_quan_ban_hanh?: string;
  ngay_ban_hanh?: string | Date;
  ngay_co_hieu_luc?: string | Date;
  tinh_trang_hieu_luc?: string;
  types?: number;
  label?: string;
  terms?: any[]; // ⭐ mục lục
}

@Injectable({
  providedIn: "root",
})
export class ChatbotService {
  // ==== LOCAL DRAFTS STORE (văn bản ảo trên client) ====
  private _localDocs = new Map<string, LocalDoc>();

  private _makeLocalId(): string {
    return "local-" + Math.random().toString(36).slice(2, 10);
  }

  createLocalDoc(doc: Omit<LocalDoc, "id">): LocalDoc {
    const id = this._makeLocalId();
    const full: LocalDoc = {
      id,
      label: "Tất cả",
      types: 3, // tương ứng TypeDocument.FromSearch
      ...doc,
    };
    this._localDocs.set(id, full);
    return full;
  }

  hasLocalDoc(id: string) {
    return this._localDocs.has(id);
  }
  getLocalDoc(id: string) {
    return this._localDocs.get(id) || null;
  }
  updateLocalDocContent(id: string, patch: Partial<any>) {
    const current = this._localDocs.get(id);
    if (!current) return null;

    const updated = { ...current, ...patch };
    this._localDocs.set(id, updated);
    return updated;
  }
  // ======================================================

  public textFormVanBan: BehaviorSubject<string> = new BehaviorSubject<string>(
    ""
  );
  public textBoiDen: Subject<string> = new Subject<string>(); //subject để không lưu giá trị cũ được bôi đen khi chatbot chưa được init

  // public listDocument: BehaviorSubject<[]> = new BehaviorSubject<[]>(null);
  public listDocument: BehaviorSubject<any[]> = new BehaviorSubject<any[]>([]);
  public openChatbot$  = new Subject<void>();
  public focusChatbot$ = new Subject<void>();
  public dockOpen$ = new BehaviorSubject<boolean>(false);
  openDrafting$ = new BehaviorSubject<{ focus?: boolean } | null>(null);
  public draftingMode: 'rewrite'|'formal'|'summarize'|'bullet'|null = null;

  open()  { this.openChatbot$.next();  }
  focus() { this.focusChatbot$.next(); }
  openDock() { this.dockOpen$.next(true); }
  closeDock() { this.dockOpen$.next(false); }
  openDrafting(opts: { focus?: boolean } = {}) {
    this.openDrafting$.next(opts);
  }
  constructor(private http: HttpClient) {}

  getConversation(workspace_id) {
    const headers = new HttpHeaders().set(InterceptorSkipHeader, "");
    const params = {
      workspace_id: workspace_id,
    };
    return this.http.get(`${environment.apiUrl}/chatbot/conversation`, {
      params: params,
      headers: headers,
    });
  }
  getMessageChatbot(idConversation) {
    const headers = new HttpHeaders().set(InterceptorSkipHeader, "");
    return this.http.get(
      `${environment.apiUrl}/chatbot/conversation/${idConversation}`,
      {
        headers: headers,
      }
    );
  }
  getQuota() {
    const headers = new HttpHeaders().set(InterceptorSkipHeader, "");
    return this.http.get<any>(`${environment.apiUrl}/package/request-count`, {
      headers: headers,
    });
  }
  renameChatbot(idConversation: string, name: string) {
    const headers = new HttpHeaders().set(InterceptorSkipHeader, "");
    return this.http.put(
      `${environment.apiUrl}/chatbot/conversation/${idConversation}/`,
      { name: name },
      { headers: headers }
    );
  }
  deleteChatbot(idConversation: string) {
    const headers = new HttpHeaders().set(InterceptorSkipHeader, "");
    return this.http.delete(
      `${environment.apiUrl}/chatbot/conversation/${idConversation}`,
      {
        headers: headers,
      }
    );
  }
  getDocIdFromSoHieu(sohieu, search_legal_term) {
    const body = {
      co_quan_ban_hanh: "",
      domain_list: "",
      don_vi: "",
      filter: "",
      limit: 100,
      loai_van_ban: "",
      must: "",
      must_not: "",
      ngay_ban_hanh_end: null,
      ngay_ban_hanh_start: null,
      order: "desc",
      page: 1,
      query: sohieu,
      search_legal_term: search_legal_term,
      should: "",
      so_hieu: "",
      sort: "loai_van_ban",
      statistic: true,
      ten_van_ban: "",
      tinh_trang_hieu_luc: "",
      sat_nhap_tinh: false,
      threshold: 0.5,
    };
    return this.http.post<any>(
      `${environment.apiUrl}/legal_search/by_query`,
      body
    );
  }
  feedbackChatbot(messageId, action) {
    const body = {
      message_id: messageId,
      feedback: action, // like or dislike
    };
    const headers = new HttpHeaders().set(InterceptorSkipHeader, "");
    return this.http.post(
      `${environment.apiUrl}/chatbot/conversation/message_feedback/`,
      body,
      {
        headers,
      }
    );
  }
  convertTextToSpeech(messageId: string) {
    return this.http.get(
      `${environment.apiUrl}/chatbot/conversation/message_tts/${messageId}/`,
      { responseType: "blob" } // lấy dạng blob (file âm thanh)
    );
  }

  updateTimeWorkspace(workspace_id: string) {
    const headers = new HttpHeaders().set(InterceptorSkipHeader, "");
    return this.http.put(
      `${environment.apiUrl}/workspace/${workspace_id}/update_time`,
      {},
      { headers: headers }
    );
  }
}
