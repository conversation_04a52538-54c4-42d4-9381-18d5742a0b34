/**
 * Constants for graph component
 */

export const NODE_COLORS = {
  DEFAULT: "#42a5f5",
  ARTICLE: "#F24822",
  DOCUMENT: "#3DADFF",
  ROOT: "#34C759",
};

// Color mapping by "loai_van_ban"
// Colors are chosen to match the legend reference provided
export const LOAI_VAN_BAN_COLORS: Record<string, string> = {
  "Hiến pháp": "#E53935", // red
  "Bộ luật": "#1E88E5", // blue
  "Luật": "#FF6B00", // orange-red (per request: <PERSON><PERSON><PERSON> is red-toned)
  "Pháp lệnh": "#FFB300", // amber
  "Lệnh": "#F4A261", // light amber
  "Sắc lệnh": "#FBC02D", // yellow
  "Nghị quyết": "#43A047", // green
  "Nghị định": "#4CAF50", // green
  "Thông tư": "#00ACC1", // teal
  "Thông tư liên tịch": "#1DE9B6", // mint
  "Quyết định": "#2962FF", // strong blue
  "Công văn": "#2196F3", // blue
  "Báo cáo": "#29B6F6", // light blue
  "Công điện": "#039BE5", // blue
  "Chỉ thị": "#00BCD4", // cyan
  "Điều ước quốc tế": "#5E35B1", // purple
  "Hướng dẫn": "#7E57C2", // purple
  "Kế hoạch": "#8E24AA", // deep purple
  "Thông báo": "#AB47BC", // purple-pink
  "Văn bản hợp nhất": "#26A69A", // teal-green
  "Dự thảo": "#8D6E63", // brown
  "Văn bản khác": "#9E9E9E", // gray
};

export const NODE_SIZES = {
  ROOT: 100,
  REGULAR: 80,
  MIN_LABEL_SIZE: 30,
} as const;

export const CHART_CONFIG = {
  FORCE: {
    REPULSION: 800,
    GRAVITY: 0.08,
    EDGE_LENGTH: 220,
  },
  LINE_STYLE: {
    WIDTH: 2,
    WIDTH_EMPHASIS: 10,
  },
  EDGE_SYMBOL: {
    SIZE: [4, 10],
    TYPES: ['circle', 'arrow'] as const,
  },
} as const;

export const RELATIONSHIP_DIRECTIONS = {
  OUTGOING: 'OUTGOING',
  INCOMING: 'INCOMING',
} as const;

export const NODE_TYPES = {
  VAN_BAN: 'VAN_BAN',
  DIEU_KHOAN: 'DIEU_KHOAN',
} as const;

export const RELATIONSHIP_TYPES = {
  BAI_BO: 'bai_bo',
  BAO_GOM: 'bao_gom',
  BO_SUNG: 'bo_sung',
  CAN_CU: 'can_cu',
  DAN_CHIEU: 'dan_chieu',
  DINH_CHI: 'dinh_chi',
  HUONG_DAN: 'huong_dan',
  HUY_BO: 'huy_bo',
  QUY_DINH_CHI_TIET: 'quy_dinh_chi_tiet',
  SUA_DOI: 'sua_doi',
  SUA_DOI_BO_SUNG: 'sua_doi_bo_sung',
  THAY_THE: 'thay_the',
} as const;

export const VALIDATION_LIMITS = {
  DEPTH: { MIN: 1, MAX: 3 },
  GLOBAL_LIMIT: { MIN: 1, MAX: 50 },
  LIMIT_PER_SEED: { MIN: 1, MAX: 10 },
} as const;

export const TOAST_CONFIG = {
  SUCCESS: {
    closeButton: true,
    positionClass: 'toast-top-right',
    toastClass: 'toast ngx-toastr',
  },
  ERROR: {
    closeButton: true,
    positionClass: 'toast-top-right',
    toastClass: 'toast ngx-toastr',
  },
} as const;

export const ERROR_MESSAGES = {
  NO_DOCUMENT_ID: 'Không tìm thấy ID tài liệu',
  NO_NODE_INFO: 'Không tìm thấy thông tin node',
  NO_NODE_ID: 'Không tìm thấy ID node',
  CANNOT_HIDE_ROOT: 'Không thể ẩn văn bản gốc',
  NO_NODES_TO_HIDE: 'Không có node mới nào được ẩn',
  NO_NODES_TO_RESTORE: 'Không có node nào để khôi phục',
  GRAPH_UPDATE_FAILED: 'Không tìm thấy dữ liệu đồ thị',
  EXPANSION_FAILED: 'Không thể mở rộng đồ thị',
} as const;

export const SUCCESS_MESSAGES = {
  GRAPH_UPDATED: 'Cập nhật dữ liệu đồ thị văn bản điều khoản',
  EXPANSION_SUCCESS: 'Mở rộng đồ thị thành công',
  NODES_HIDDEN: 'Đã ẩn các node liên quan',
  NODES_RESTORED: 'Khôi phục các node thành công',
} as const;

export const INFO_MESSAGES = {
  NO_NEW_NODES_HIDDEN: 'Không có node mới nào được ẩn',
} as const;

/**
 * Filter options for graph component
 */
export interface FilterOption {
  label: string;
  value: string;
}

export const TINH_TRANG_HIEU_LUC_OPTIONS: FilterOption[] = [
  "Còn hiệu lực",
  "Hết hiệu lực toàn bộ",
  "Không còn phù hợp",
  "Chưa có hiệu lực",
  "Ngưng hiệu lực",
  "Không xác định",
  "Hết hiệu lực một phần",
  "Chưa xác định",
].map((v) => ({ label: v, value: v }));

export const BO_LOC_LOAI_VAN_BAN_OPTIONS: FilterOption[] = [
  "Hiến pháp",
  "Bộ luật",
  "Nghị quyết",
  "Nghị Quyết",
  "Quốc hội",
  "Lệnh",
  "Nghị định",
  "Thông tư",
  "Thông tư liên tịch",
  "Thông tư liên bộ",
  "Quyết định",
  "Luật",
  "Pháp lệnh",
  "Sắc lệnh",
  "Chỉ thị",
  "Sắc luật",
  "Nghị quyết liên tịch",
  "Chương trình",
  "Văn bản hợp nhất",
  "Công văn",
  "Thông báo",
  "Bản ghi nhớ",
  "Hiệp định",
  "Văn bản khác",
  "Dự thảo",
  "Hướng dẫn",
  "Điều ước quốc tế",
  "Điều ước",
  "Báo cáo",
  "Kế hoạch",
  "Thoả thuận",
  "Công điện",
  "Thông tri",
  "Quy định",
  "Quy chế",
  "Văn bản liên quan",
  "Điều lệ",
  "Nghị định thư",
  "Công ước",
].map((v) => ({ label: v, value: v }));

export const CO_QUAN_BAN_HANH_OPTIONS: FilterOption[] = [
  "Ban Chấp hành Trung ương",
  "Ban Chỉ đạo Trung ương các chương trình mục tiêu quốc gia giai đoạn 2021-2025",
  "Ban Chỉ đạo kiểm kê Trung ương",
  "Ban Việt kiều Trung ương",
  "Bảo hiểm xã hội Việt Nam",
  "Bộ Bưu chính Viễn thông",
  "Bộ Công an",
  "Bộ Công nghiệp",
  "Bộ Công thương",
  "Bộ Chính trị",
  "Bộ Dân tộc và Tôn giáo",
  "Bộ Đại học Trung học chuyên nghiệp và Dạy nghề",
  "Bộ Đại học và Trung học chuyên nghiệp",
  "Bộ Điện lực",
  "Bộ Giáo dục",
  "Bộ Giáo dục và Đào tạo",
  "Bộ Giao thông và Bưu điện",
  "Bộ Giao thông vận tải",
  "Bộ Kế hoạch và Đầu tư",
  "Bộ Khoa học và Công nghệ",
  "Bộ Khoa học Công nghệ và Môi trường",
  "Bộ Kinh tế đối ngoại",
  "Bộ Lao động",
  "Bộ Lao động - Thương binh và Xã hội",
  "Bộ Lâm nghiệp",
  "Bộ Mỏ và Than",
  "Bộ Năng lượng",
  "Bộ Ngoại giao",
  "Bộ Ngoại thương",
  "Bộ Nội thương",
  "Bộ Nội vụ",
  "Bộ Nông nghiệp",
  "Bộ Nông nghiệp và Môi trường",
  "Bộ Nông nghiệp và Phát triển nông thôn",
  "Bộ Quốc phòng",
  "Bộ Tài chính",
  "Bộ Tài nguyên và Môi trường",
  "Bộ Thông tin",
  "Bộ Thông tin và Truyền thông",
  "Bộ Thương binh và xã hội",
  "Bộ Thương mại",
  "Bộ Thương nghiệp",
  "Bộ Thuỷ lợi",
  "Bộ Thuỷ sản",
  "Bộ trưởng",
  "Bộ trưởng Tổng thư ký",
  "Bộ Tư pháp",
  "Bộ Văn hoá",
  "Bộ Văn hoá và Thông tin",
  "Bộ Văn hoá Thông tin Thể thao và Du lịch",
  "Bộ Văn hóa - Thể thao và Du lịch",
  "Bộ Vật tư",
  "Bộ Xây dựng",
  "Bộ Xã hội",
  "Bộ Y tế",
  "Chính phủ",
  "Chủ tịch Hội đồng Bộ trưởng",
  "Chủ tịch nước",
  "Cục Dự trữ Quốc gia",
  "Cục Hàng không dân dụng Việt Nam",
  "Cục Hàng không Việt Nam",
  "Cục Hải quan",
  "Cục Lưu trữ Nhà nước",
  "Cục Quản lý dược",
  "Cục Thống kê",
  "Đoàn Chủ tịch Ủy ban Trung ương Mặt trận Tổ quốc Việt Nam",
  "HĐND tỉnh Vĩnh Phúc",
  "Hội đồng Bộ trưởng",
  "Hội đồng Nhà nước",
  "Hội đồng Thẩm phán Tòa án Nhân dân Tối cao",
  "Hội đồng Trọng tài kinh tế Nhà nước",
  "Kiểm toán Nhà nước",
  "Ngân hàng Nhà nước",
  "Ngân hàng quốc gia",
  "Phủ Thủ tướng",
  "Quốc hội",
  "Thanh tra Chính phủ",
  "Thanh tra Nhà nước",
  "Thứ trưởng",
  "Thủ tướng Chính phủ",
  "Tòa án nhân dân tối cao",
  "Tổng Công đoàn Việt Nam",
  "Tổng cục Bưu điện",
  "Tổng cục Địa chính",
  "Tổng cục Du lịch",
  "Tổng cục Hải quan",
  "Tổng cục Lâm nghiệp",
  "Tổng cục Quản lý ruộng đất",
  "Tổng cục Thuế",
  "Tổng liên đoàn Lao động Việt Nam",
  "UBND thành phố Hà Nội",
  "UBND thành phố Hồ Chí Minh",
  "UBND thành phố Huế",
  "Uỷ ban Bảo vệ bà mẹ và trẻ em",
  "Uỷ ban Bảo vệ bà mẹ trẻ em",
  "Uỷ ban Bảo vệ và chăm sóc trẻ em Việt Nam",
  "Uỷ ban Chứng khoán Nhà nước",
  "Uỷ ban Dân số Gia đình và Trẻ em",
  "Uỷ ban Dân tộc",
  "Uỷ ban Dân tộc và Miền núi",
  "Uỷ ban Kế hoạch Nhà nước",
  "Uỷ ban Khoa học và Kỹ thuật Nhà nước",
  "Uỷ ban Khoa học Nhà nước",
  "Uỷ ban Nông nghiệp Trung ương",
  "Uỷ ban Nhà nước về hợp tác và đầu tư",
  "Uỷ ban Quốc gia Dân số và Kế hoạch hoá gia đình",
  "Uỷ ban Quốc gia vì sự tiến bộ của phụ nữ Việt Nam",
  "Uỷ ban Thể dục Thể thao",
  "Uỷ ban Thể dục thể thao",
  "Uỷ ban Trung ương Mặt trận Tổ quốc Việt Nam",
  "Uỷ ban Vật giá Nhà nước",
  "Uỷ ban xây dựng cơ bản Nhà nước",
  "Ủy ban Dân tộc",
  "Ủy ban Thường vụ Quốc hội",
  "Văn phòng Chính phủ",
  "Viện kiểm sát nhân dân tối cao",
].map((v) => ({ label: v, value: v }));

export const BO_LOC_MOI_QUAN_HE_OPTIONS: FilterOption[] = [
  { label: "Bao gồm", value: "bao_gom" },
  { label: "Dẫn chiếu", value: "dan_chieu" },
  { label: "Bãi bỏ", value: "bai_bo" },
  { label: "Bổ sung", value: "bo_sung" },
  { label: "Đình chỉ", value: "dinh_chi" },
  { label: "Hướng dẫn", value: "huong_dan" },
  { label: "Quy định chi tiết", value: "quy_dinh_chi_tiet" },
  { label: "Sửa đổi", value: "sua_doi" },
  { label: "Sửa đổi, bổ sung", value: "sua_doi_bo_sung" },
  { label: "Thay thế", value: "thay_the" },
  { label: "Căn cứ", value: "can_cu" },
  { label: "Hủy bỏ", value: "huy_bo" },
];

