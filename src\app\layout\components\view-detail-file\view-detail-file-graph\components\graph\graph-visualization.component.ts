import { Component, Input, ViewChild, ElementRef, OnChanges, SimpleChanges, OnDestroy, AfterViewInit } from '@angular/core';
import { GraphNode, GraphLink, ApiNode } from '../../types/graph.types';
import { GraphRendererService } from '../../services/graph-renderer.service';
import { GraphRendererCallbacks, D3GraphNode, D3GraphLink } from '../../models/graph-renderer.models';

@Component({
  selector: 'app-graph-visualization',
  templateUrl: './graph-visualization.component.html',
  styleUrls: ['./graph-visualization.component.scss'],
})
export class GraphVisualizationComponent implements OnChanges, OnDestroy, AfterViewInit {
  @Input() nodes: GraphNode[] = [];
  @Input() links: GraphLink[] = [];
  @Input() apiNodeMap: Map<string, ApiNode> = new Map();
  @Input() rootNodeId: string = '';
  @Input() isLoading: boolean = false;
  @Input() d3Nodes: D3GraphNode[] = [];
  @Input() d3Links: D3GraphLink[] = [];
  @Input() callbacks?: GraphRendererCallbacks;
  @Input() isTimKiemMode: boolean = false;

  @ViewChild('d3GraphContainer', { static: false })
  d3GraphContainer?: ElementRef<HTMLDivElement>;

  private hasRendered: boolean = false;

  constructor(private rendererService: GraphRendererService) {}

  ngAfterViewInit(): void {
    // Render graph when view is initialized (important when component is recreated after *ngIf)
    if (this.nodes.length && !this.hasRendered) {
      // Use setTimeout to ensure ViewChild is available
      setTimeout(() => {
        this.renderGraph();
      }, 0);
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (
      (changes['nodes'] || changes['links'] || changes['d3Nodes'] || changes['d3Links']) &&
      this.d3GraphContainer
    ) {
      this.renderGraph();
    }
  }

  private renderGraph(): void {
    if (!this.d3GraphContainer || !this.nodes.length) {
      if (this.d3GraphContainer) {
        this.rendererService.destroy();
        this.d3GraphContainer.nativeElement.innerHTML = '';
      }
      this.hasRendered = false;
      return;
    }

    this.rendererService.renderGraph(
      this.d3GraphContainer.nativeElement,
      this.nodes,
      this.links,
      this.apiNodeMap,
      this.rootNodeId,
      this.d3Nodes,
      this.d3Links,
      this.callbacks,
      this.isTimKiemMode
    );
    this.hasRendered = true;
  }

  ngOnDestroy(): void {
    this.rendererService.destroy();
    this.hasRendered = false;
  }
}

